#!/bin/bash

# 音频转文字AI工具启动脚本
# 使用text2video conda环境

echo "============================================================"
echo "启动音频转文字AI工具"
echo "============================================================"

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "错误: conda未找到，请确保已安装Anaconda或Miniconda"
    exit 1
fi

# 激活conda环境
echo "激活text2video环境..."
source /home/<USER>/database/anaconda3/etc/profile.d/conda.sh
conda activate text2video

# 检查环境是否激活成功
if [ "$CONDA_DEFAULT_ENV" != "text2video" ]; then
    echo "错误: 无法激活text2video环境"
    echo "请确保环境已正确创建: conda create -n text2video python=3.10"
    exit 1
fi

echo "环境激活成功: $CONDA_DEFAULT_ENV"

# 切换到项目目录
cd /userfiles/mtc/code_hub/video2text

# 检查项目文件是否存在
if [ ! -f "run_ai.py" ]; then
    echo "错误: 找不到run_ai.py文件"
    echo "请确保在正确的项目目录中"
    exit 1
fi

echo "启动应用..."
echo "访问地址: http://localhost:5000"
echo "按 Ctrl+C 停止应用"
echo "============================================================"

# 启动应用
python run_ai.py
