"""GPU检测和设备管理工具"""
import torch
import platform
import subprocess

def get_gpu_info():
    """获取GPU信息"""
    gpu_info = {
        'available': False,
        'cuda_available': False,
        'device_count': 0,
        'device_name': 'CPU',
        'device_type': 'cpu',
        'memory_allocated': 0,
        'memory_reserved': 0,
        'driver_version': None,
        'cuda_version': None
    }
    
    try:
        # 检查CUDA是否可用
        if torch.cuda.is_available():
            gpu_info['available'] = True
            gpu_info['cuda_available'] = True
            gpu_info['device_count'] = torch.cuda.device_count()
            gpu_info['device_type'] = 'cuda'
            
            # 获取第一个GPU的信息
            if gpu_info['device_count'] > 0:
                gpu_info['device_name'] = torch.cuda.get_device_name(0)
                # 获取显存信息（字节转MB）
                gpu_info['memory_allocated'] = torch.cuda.memory_allocated(0) / 1024 / 1024
                gpu_info['memory_reserved'] = torch.cuda.memory_reserved(0) / 1024 / 1024
                
            # 获取CUDA版本
            gpu_info['cuda_version'] = torch.version.cuda
            
            # 尝试获取驱动版本（仅限Windows/Linux）
            try:
                if platform.system() == 'Windows':
                    result = subprocess.run(['nvidia-smi', '--query-gpu=driver_version', '--format=csv,noheader'], 
                                         capture_output=True, text=True)
                    if result.returncode == 0:
                        gpu_info['driver_version'] = result.stdout.strip()
                elif platform.system() == 'Linux':
                    result = subprocess.run(['nvidia-smi', '--query-gpu=driver_version', '--format=csv,noheader'], 
                                         capture_output=True, text=True)
                    if result.returncode == 0:
                        gpu_info['driver_version'] = result.stdout.strip()
            except:
                pass
                
    except Exception as e:
        print(f"GPU检测出错: {e}")
    
    return gpu_info

def get_optimal_device():
    """获取最优设备（优先GPU）"""
    if torch.cuda.is_available():
        device = 'cuda'
        print("✓ 使用GPU加速")
        # 清理GPU缓存
        torch.cuda.empty_cache()
    else:
        device = 'cpu'
        print("→ 使用CPU处理（未检测到可用GPU）")
    
    return device

def print_device_info():
    """打印设备信息"""
    gpu_info = get_gpu_info()
    
    print("\n=== 设备信息 ===")
    if gpu_info['cuda_available']:
        print(f"GPU状态: 可用")
        print(f"GPU型号: {gpu_info['device_name']}")
        print(f"GPU数量: {gpu_info['device_count']}")
        print(f"CUDA版本: {gpu_info['cuda_version']}")
        if gpu_info['driver_version']:
            print(f"驱动版本: {gpu_info['driver_version']}")
        print(f"显存使用: {gpu_info['memory_allocated']:.1f}MB / {gpu_info['memory_reserved']:.1f}MB")
    else:
        print("GPU状态: 不可用")
        print("将使用CPU进行处理")
    print("================\n")
    
    return gpu_info

def setup_whisper_device(model):
    """为Whisper模型设置最优设备"""
    device = get_optimal_device()
    
    # 统一使用float32以避免兼容性问题
    model = model.float()
    
    # 如果使用GPU，确保模型在GPU上
    if device == 'cuda':
        model = model.cuda()
        print("→ GPU模式：使用FP32精度")
    else:
        # 确保CPU模型使用float32
        model = model.cpu()
        print("→ CPU模式：使用FP32精度")
    
    return model, device

def monitor_gpu_memory():
    """监控GPU内存使用"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated(0) / 1024 / 1024  # MB
        reserved = torch.cuda.memory_reserved(0) / 1024 / 1024    # MB
        return {
            'allocated': allocated,
            'reserved': reserved,
            'free': reserved - allocated
        }
    return None

def clear_gpu_cache():
    """清理GPU缓存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("→ GPU缓存已清理")