<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频转文字工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <h1>音频转文字工具</h1>
        
        <div class="upload-section">
            <h2>上传音频文件</h2>
            <form id="uploadForm">
                <div class="file-input-wrapper">
                    <input type="file" id="audioFile" accept=".wav,.mp3,.mp4,.m4a,.wma,.aac" required>
                    <label for="audioFile" class="file-label">选择音频文件</label>
                    <span id="fileName" class="file-name">未选择文件</span>
                </div>
                
                <div class="format-options">
                    <label>
                        <input type="radio" name="format" value="text" checked>
                        纯文本
                    </label>
                    <label>
                        <input type="radio" name="format" value="srt">
                        字幕格式 (SRT)
                    </label>
                </div>
                
                <button type="submit" id="uploadBtn" class="btn btn-primary">开始转换</button>
            </form>
            
            <div id="progress" class="progress hidden">
                <div class="progress-bar"></div>
                <div class="progress-info">
                    <span class="progress-text">准备中...</span>
                    <span class="progress-percentage">0%</span>
                </div>
            </div>
        </div>
        
        <div id="results" class="results-section hidden">
            <h2>转换结果</h2>
            <div class="result-tabs">
                <button class="tab-btn active" data-tab="text">文本内容</button>
                <button class="tab-btn" data-tab="tools">AI工具</button>
                <button class="tab-btn" data-tab="subtitle">双语字幕</button>
            </div>
            
            <div id="textTab" class="tab-content active">
                <textarea id="transcriptionText" class="result-textarea" readonly></textarea>
                <button id="downloadText" class="btn btn-secondary">下载文件</button>
            </div>
            
            <div id="toolsTab" class="tab-content">
                <div class="tool-section">
                    <h3>AI总结</h3>
                    <button id="summarizeBtn" class="btn btn-primary">生成总结</button>
                    <div id="summaryResult" class="tool-result hidden">
                        <textarea id="summaryText" class="result-textarea" readonly></textarea>
                    </div>
                </div>
            </div>
            
            <div id="subtitleTab" class="tab-content">
                <div class="subtitle-section">
                    <h3>生成双语字幕</h3>
                    <p>将中文字幕翻译为英文，生成双语字幕文件</p>
                    <button id="generateDualSubtitle" class="btn btn-primary">生成双语字幕</button>
                    <div id="dualSubtitleResult" class="tool-result hidden">
                        <textarea id="dualSubtitleText" class="result-textarea" readonly></textarea>
                        <button id="downloadDualSubtitle" class="btn btn-secondary">下载双语字幕</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>