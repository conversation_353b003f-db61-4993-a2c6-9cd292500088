<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频转文字AI工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style_ai.css') }}">
</head>
<body>
    <div class="container">
        <h1>音频转文字AI工具</h1>
        
        <div class="upload-section">
            <h2>上传音频文件</h2>
            <form id="uploadForm">
                <div class="file-input-wrapper">
                    <input type="file" id="audioFile" accept=".wav,.mp3,.mp4,.m4a,.wma,.aac" required>
                    <label for="audioFile" class="file-label">选择音频文件</label>
                    <span id="fileName" class="file-name">未选择文件</span>
                </div>
                
                <div class="format-options">
                    <label>
                        <input type="radio" name="format" value="text" checked>
                        纯文本
                    </label>
                    <label>
                        <input type="radio" name="format" value="srt">
                        字幕格式 (SRT)
                    </label>
                </div>
                
                <div class="model-options">
                    <label for="modelSelect">选择Whisper模型：</label>
                    <select id="modelSelect" class="model-select">
                        <option value="small">Whisper Small (速度快)</option>
                        <option value="large-v3">Whisper Large-v3 (准确度高)</option>
                    </select>
                </div>
                
                <button type="submit" id="uploadBtn" class="btn btn-primary">开始转换</button>
            </form>
            
            <div id="progress" class="progress hidden">
                <div class="progress-bar"></div>
                <div class="progress-info">
                    <span class="progress-text">准备中...</span>
                    <span class="progress-percentage">0%</span>
                </div>
            </div>
        </div>
        
        <div id="results" class="results-section hidden">
            <h2>转换结果</h2>
            <div class="result-tabs">
                <button class="tab-btn active" data-tab="text">文本内容</button>
                <button class="tab-btn" data-tab="tools">AI工具</button>
                <button class="tab-btn" data-tab="subtitle">双语字幕</button>
            </div>
            
            <div id="textTab" class="tab-content active">
                <textarea id="transcriptionText" class="result-textarea" readonly></textarea>
                <button id="downloadText" class="btn btn-secondary">下载文件</button>
            </div>
            
            <div id="toolsTab" class="tab-content">
                <div class="ai-tools-section">
                    <h3>选择AI工具</h3>
                    <div class="tool-selector">
                        <select id="aiToolSelect" class="tool-select">
                            <option value="summarize">总结摘要</option>
                            <option value="outline">提取大纲</option>
                            <option value="keywords">关键词提取</option>
                            <option value="polish">文本润色</option>
                            <option value="translate">翻译</option>
                            <option value="custom">自定义处理</option>
                        </select>
                    </div>
                    
                    <div id="customPromptSection" class="custom-prompt-section hidden">
                        <h4>自定义提示语</h4>
                        <textarea id="customPrompt" class="prompt-textarea" 
                                  placeholder="例如：请将这段对话整理成会议纪要，包含时间、参与人、主要议题和行动项..."></textarea>
                    </div>
                    
                    <div class="preset-prompts hidden" id="presetPrompts">
                        <h4>快速选择提示语模板</h4>
                        <div class="prompt-templates">
                            <button class="prompt-template" data-prompt="请将这段对话整理成会议纪要">会议纪要</button>
                            <button class="prompt-template" data-prompt="请提取这段内容的核心观点和论据">观点提取</button>
                            <button class="prompt-template" data-prompt="请将这段内容改写成新闻稿格式">新闻稿</button>
                            <button class="prompt-template" data-prompt="请分析这段内容的情感倾向">情感分析</button>
                        </div>
                    </div>
                    
                    <button id="processBtn" class="btn btn-primary">开始处理</button>
                    
                    <div id="aiResult" class="ai-result hidden">
                        <h4 id="aiResultTitle">处理结果</h4>
                        <div id="aiResultText" class="result-textarea" style="overflow-y: auto; background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; min-height: 200px;"></div>
                        <div class="result-actions">
                            <button id="copyResult" class="btn btn-secondary">复制结果</button>
                            <button id="downloadResult" class="btn btn-secondary">下载结果</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="subtitleTab" class="tab-content">
                <div class="subtitle-section">
                    <h3>生成双语字幕</h3>
                    <p>将字幕翻译为目标语言，生成双语字幕文件</p>
                    <div class="language-options">
                        <label>目标语言：</label>
                        <select id="targetLanguage" class="language-select">
                            <option value="en">英语</option>
                            <option value="zh">中文</option>
                            <option value="ja">日语</option>
                            <option value="ko">韩语</option>
                            <option value="es">西班牙语</option>
                            <option value="fr">法语</option>
                        </select>
                    </div>
                    <button id="generateDualSubtitle" class="btn btn-primary">生成双语字幕</button>
                    <div id="subtitleProgress" class="progress hidden">
                        <div class="progress-bar"></div>
                        <div class="progress-info">
                            <span class="progress-text">正在翻译字幕...</span>
                            <span class="progress-percentage">0%</span>
                        </div>
                    </div>
                    <div id="dualSubtitleResult" class="tool-result hidden">
                        <textarea id="dualSubtitleText" class="result-textarea" readonly></textarea>
                        <button id="downloadDualSubtitle" class="btn btn-secondary">下载双语字幕</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/main_ai.js') }}"></script>
</body>
</html>