@echo off
title Audio to Text AI Tool

echo ============================================================
echo                    Audio to Text AI Tool                    
echo                    AI Enhanced Version                      
echo ============================================================
echo.

REM Check Python installation
echo [1/4] Checking Python environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [X] Error: Python not found
    echo.
    echo Please install Python 3.8 or higher
    echo Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [OK] Python %PYTHON_VERSION% installed

REM Check dependencies
echo.
echo [2/4] Checking dependencies...
python -c "import flask" >nul 2>&1
if %errorlevel% neq 0 (
    goto :install_deps
)
python -c "import whisper" >nul 2>&1
if %errorlevel% neq 0 (
    goto :install_deps
)
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    goto :install_deps
)
echo [OK] Dependencies checked
goto :check_model

:install_deps
echo [X] Missing dependencies, installing...
echo.
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo.
    echo [X] Installation failed, please check network connection
    pause
    exit /b 1
)
echo.
echo [OK] Dependencies installed

:check_model
REM Check Whisper model
echo.
echo [3/4] Checking Whisper model...
if exist "models\whisper-small\pytorch_model.bin" (
    echo [OK] Found local model: whisper-small
) else if exist "models\whisper\small.pt" (
    echo [OK] Found local model: small.pt
) else (
    echo [!] No local model found, will download on first run
)

REM Display features
echo.
echo [4/4] Starting AI Enhanced Version...
echo.
echo ============================================================
echo Features:                                                 
echo - Audio to Text (multiple formats supported)               
echo - AI Processing (Summary, Outline, Keywords, Polish)       
echo - Custom AI Prompts                                        
echo - Multi-language Translation and Dual Subtitles            
echo - Real-time Progress Display                               
echo ============================================================
echo.
echo AI Model: deepseek-ai/DeepSeek-V3
echo Whisper Model: Small
echo.
echo ------------------------------------------------------------
echo Starting application...
echo Access URL: http://localhost:5000
echo Press Ctrl+C to stop
echo ------------------------------------------------------------
echo.

REM Start AI enhanced version
python run_ai.py
if %errorlevel% neq 0 (
    echo.
    echo [X] Application failed to start
    echo.
    echo Possible reasons:
    echo 1. Port 5000 is already in use
    echo 2. File run_ai.py not found
    echo 3. Python environment issues
    echo.
    echo Trying basic version:
    python app.py
)

echo.
echo Application stopped
pause