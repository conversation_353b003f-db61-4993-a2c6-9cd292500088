#!/usr/bin/env python
"""启动带AI功能的音频转文字应用"""
import os
import sys
import subprocess

def check_requirements():
    """检查必要的依赖"""
    required_packages = {
        'flask': 'Flask',
        'whisper': 'openai-whisper',
        'requests': 'requests'
    }
    
    missing = []
    for module, package in required_packages.items():
        try:
            __import__(module)
        except ImportError:
            missing.append(package)
    
    if missing:
        print("缺少以下依赖包：")
        for pkg in missing:
            print(f"  - {pkg}")
        print("\n正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install"] + missing)
        print("\n安装完成！")
    
    return True

def main():
    print("="*60)
    print("音频转文字AI工具 - 启动器")
    print("="*60)
    print("\n功能特点：")
    print("  ✓ 音频转文字（使用OpenAI Whisper）")
    print("  ✓ AI文本处理（使用DeepSeek-V3）")
    print("  ✓ 自定义提示语支持")
    print("  ✓ 多种AI工具：总结、大纲、关键词、润色、翻译")
    print("  ✓ 双语字幕生成")
    print("\nAI配置：")
    print("  - API: SiliconFlow")
    print("  - 模型: deepseek-ai/DeepSeek-V3")
    print("\n" + "="*60)
    
    # 检查依赖
    print("\n检查依赖...")
    check_requirements()
    
    # 确保目录存在
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("outputs", exist_ok=True)
    os.makedirs("models/whisper", exist_ok=True)
    
    # 启动应用
    print("\n正在启动应用...")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止应用\n")
    
    try:
        subprocess.run([sys.executable, "app_with_ai.py"])
    except KeyboardInterrupt:
        print("\n\n应用已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        print("请检查 app_with_ai.py 文件是否存在")

if __name__ == "__main__":
    main()