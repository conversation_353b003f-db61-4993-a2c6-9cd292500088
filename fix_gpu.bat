@echo off
echo ============================================================
echo                   修复GPU支持 - PyTorch CUDA版本安装
echo ============================================================
echo.

echo [1/3] 卸载当前的CPU版本PyTorch...
echo.
pip uninstall -y torch torchvision torchaudio
if %errorlevel% neq 0 (
    echo [X] 卸载失败
    pause
    exit /b 1
)
echo [OK] CPU版本已卸载
echo.

echo [2/3] 安装CUDA 11.8版本的PyTorch...
echo 注意：这可能需要几分钟时间，请耐心等待...
echo.
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
if %errorlevel% neq 0 (
    echo.
    echo [X] 安装失败，可能的原因：
    echo    1. 网络连接问题
    echo    2. 磁盘空间不足
    echo    3. 权限问题
    echo.
    echo 备选方案：使用国内镜像
    echo pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
    pause
    exit /b 1
)
echo.
echo [OK] CUDA版本PyTorch安装成功！
echo.

echo [3/3] 验证GPU支持...
echo.
python check_gpu.py

echo.
echo ============================================================
echo 安装完成！
echo 如果仍然无法检测到GPU，请确保：
echo 1. 已安装NVIDIA显卡驱动
echo 2. 显卡支持CUDA（GTX 600系列及以上）
echo ============================================================
pause