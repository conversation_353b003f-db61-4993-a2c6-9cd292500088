[{"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "send_file", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "send_file", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "send_file", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "whisper", "kind": 6, "isExtraImport": true, "importPath": "whisper", "description": "whisper", "detail": "whisper", "documentation": {}}, {"label": "openai", "kind": 6, "isExtraImport": true, "importPath": "openai", "description": "openai", "detail": "openai", "documentation": {}}, {"label": "secure_filename", "importPath": "werkzeug.utils", "description": "werkzeug.utils", "isExtraImport": true, "detail": "werkzeug.utils", "documentation": {}}, {"label": "secure_filename", "importPath": "werkzeug.utils", "description": "werkzeug.utils", "isExtraImport": true, "detail": "werkzeug.utils", "documentation": {}}, {"label": "secure_filename", "importPath": "werkzeug.utils", "description": "werkzeug.utils", "isExtraImport": true, "detail": "werkzeug.utils", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "WHISPER_MODELS", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "DEFAULT_MODEL", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "MODEL_DIR", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "ALLOWED_EXTENSIONS", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "MAX_FILE_SIZE", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "SUPPORTED_LANGUAGES", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "DEFAULT_LANGUAGE", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "DEFAULT_MODEL", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "WHISPER_MODELS", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "print_device_info", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "setup_whisper_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "get_optimal_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "clear_gpu_cache", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "print_device_info", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "setup_whisper_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "get_optimal_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "clear_gpu_cache", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "print_device_info", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "setup_whisper_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "get_optimal_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "clear_gpu_cache", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "print_device_info", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "setup_whisper_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "get_optimal_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "get_optimal_device", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "clear_gpu_cache", "importPath": "gpu_utils", "description": "gpu_utils", "isExtraImport": true, "detail": "gpu_utils", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "pathlib", "kind": 6, "isExtraImport": true, "importPath": "pathlib", "description": "pathlib", "detail": "pathlib", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "AI_CONFIG", "importPath": "ai_config", "description": "ai_config", "isExtraImport": true, "detail": "ai_config", "documentation": {}}, {"label": "AI_PROMPTS", "importPath": "ai_config", "description": "ai_config", "isExtraImport": true, "detail": "ai_config", "documentation": {}}, {"label": "ERROR_MESSAGES", "importPath": "ai_config", "description": "ai_config", "isExtraImport": true, "detail": "ai_config", "documentation": {}}, {"label": "safe_load_whisper_model", "importPath": "whisper_utils", "description": "whisper_utils", "isExtraImport": true, "detail": "whisper_utils", "documentation": {}}, {"label": "safe_transcribe", "importPath": "whisper_utils", "description": "whisper_utils", "isExtraImport": true, "detail": "whisper_utils", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "platform", "kind": 6, "isExtraImport": true, "importPath": "platform", "description": "platform", "detail": "platform", "documentation": {}}, {"label": "tqdm", "importPath": "tqdm", "description": "tqdm", "isExtraImport": true, "detail": "tqdm", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "re", "kind": 6, "isExtraImport": true, "importPath": "re", "description": "re", "detail": "re", "documentation": {}}, {"label": "copy", "kind": 6, "isExtraImport": true, "importPath": "copy", "description": "copy", "detail": "copy", "documentation": {}}, {"label": "download_model", "importPath": "download_models", "description": "download_models", "isExtraImport": true, "detail": "download_models", "documentation": {}}, {"label": "get_model_path", "importPath": "download_models", "description": "download_models", "isExtraImport": true, "detail": "download_models", "documentation": {}}, {"label": "list_downloaded_models", "importPath": "download_models", "description": "download_models", "isExtraImport": true, "detail": "download_models", "documentation": {}}, {"label": "wave", "kind": 6, "isExtraImport": true, "importPath": "wave", "description": "wave", "detail": "wave", "documentation": {}}, {"label": "AI_CONFIG", "kind": 5, "importPath": "ai_config", "description": "ai_config", "peekOfCode": "AI_CONFIG = {\n    \"base_url\": \"https://api.siliconflow.cn/v1/chat/completions\",\n    \"api_key\": \"sk-whlteqfyhscyzpbquubcqkrvvxxqwunqbziyscbrnrshysnr\",\n    \"model\": \"deepseek-ai/DeepSeek-V3\",\n    \"default_temperature\": 0.7,\n    \"max_tokens\": 2000\n}\n# 预设的AI工具提示语模板\nAI_PROMPTS = {\n    \"summarize\": {", "detail": "ai_config", "documentation": {}}, {"label": "AI_PROMPTS", "kind": 5, "importPath": "ai_config", "description": "ai_config", "peekOfCode": "AI_PROMPTS = {\n    \"summarize\": {\n        \"name\": \"总结摘要\",\n        \"description\": \"生成文本的简洁摘要\",\n        \"default_prompt\": \"请为以下内容生成一个简洁的摘要，突出关键信息：\\n\\n{text}\"\n    },\n    \"outline\": {\n        \"name\": \"提取大纲\",\n        \"description\": \"提取内容的主要观点和结构\",\n        \"default_prompt\": \"请分析以下内容，提取主要观点并生成结构化大纲：\\n\\n{text}\"", "detail": "ai_config", "documentation": {}}, {"label": "ERROR_MESSAGES", "kind": 5, "importPath": "ai_config", "description": "ai_config", "peekOfCode": "ERROR_MESSAGES = {\n    \"api_error\": \"AI服务暂时不可用，请稍后重试\",\n    \"empty_text\": \"请先进行音频转文字\",\n    \"empty_prompt\": \"请输入提示语\",\n    \"request_failed\": \"请求失败，请检查网络连接\"\n}", "detail": "ai_config", "documentation": {}}, {"label": "load_whisper_model", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def load_whisper_model(model_name):\n    \"\"\"加载Whisper模型，优先使用本地模型\"\"\"\n    # 检查多个可能的模型位置\n    possible_paths = [\n        # 1. 标准whisper格式 (.pt文件)\n        os.path.join(MODEL_DIR, f\"{model_name}.pt\"),\n        os.path.join(MODEL_DIR, f\"{model_name}-v2.pt\"),\n        # 2. Hugging Face格式 (pytorch_model.bin)\n        os.path.join(\"models\", f\"whisper-{model_name}\", \"pytorch_model.bin\"),\n        os.path.join(MODEL_DIR, f\"{model_name}\", \"pytorch_model.bin\"),", "detail": "app", "documentation": {}}, {"label": "allowed_file", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def allowed_file(filename):\n    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS\***********('/')\ndef index():\n    return render_template('index.html')\***********('/upload', methods=['POST'])\ndef upload_file():\n    if 'file' not in request.files:\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']", "detail": "app", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def index():\n    return render_template('index.html')\***********('/upload', methods=['POST'])\ndef upload_file():\n    if 'file' not in request.files:\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']\n    if file.filename == '':\n        return jsonify({'error': 'No selected file'}), 400\n    if file and allowed_file(file.filename):", "detail": "app", "documentation": {}}, {"label": "upload_file", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def upload_file():\n    if 'file' not in request.files:\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']\n    if file.filename == '':\n        return jsonify({'error': 'No selected file'}), 400\n    if file and allowed_file(file.filename):\n        filename = secure_filename(file.filename)\n        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)\n        file.save(filepath)", "detail": "app", "documentation": {}}, {"label": "translate_text", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def translate_text():\n    data = request.get_json()\n    text = data.get('text', '')\n    target_lang = data.get('target', 'en')\n    if not text:\n        return jsonify({'error': 'No text provided'}), 400\n    try:\n        # 这里可以集成翻译API，暂时返回示例\n        # 实际应用中可以使用Google Translate API或其他翻译服务\n        translated_text = f\"[Translation to {target_lang}]: {text}\"", "detail": "app", "documentation": {}}, {"label": "summarize_text", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def summarize_text():\n    data = request.get_json()\n    text = data.get('text', '')\n    if not text:\n        return jsonify({'error': 'No text provided'}), 400\n    try:\n        # 使用OpenAI API进行总结（需要配置API密钥）\n        # 这里提供一个简单的示例，实际使用时需要配置OpenAI API\n        summary = f\"Summary: {text[:200]}...\"  # 简单截取前200字符作为示例\n        return jsonify({", "detail": "app", "documentation": {}}, {"label": "generate_dual_subtitle", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def generate_dual_subtitle():\n    data = request.get_json()\n    segments = data.get('segments', [])\n    if not segments:\n        return jsonify({'error': 'No segments provided'}), 400\n    try:\n        # 生成双语字幕\n        dual_srt = generate_dual_language_srt(segments)\n        # 保存文件\n        filename = f\"dual_subtitle_{datetime.now().strftime('%Y%m%d_%H%M%S')}.srt\"", "detail": "app", "documentation": {}}, {"label": "download_file", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def download_file(filename):\n    try:\n        return send_file(\n            os.path.join(app.config['OUTPUT_FOLDER'], filename),\n            as_attachment=True,\n            download_name=filename\n        )\n    except Exception as e:\n        return jsonify({'error': str(e)}), 404\ndef generate_srt(transcription_result):", "detail": "app", "documentation": {}}, {"label": "generate_srt", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def generate_srt(transcription_result):\n    srt_content = []\n    for i, segment in enumerate(transcription_result['segments'], 1):\n        start_time = format_timestamp(segment['start'])\n        end_time = format_timestamp(segment['end'])\n        text = segment['text'].strip()\n        srt_content.append(f\"{i}\")\n        srt_content.append(f\"{start_time} --> {end_time}\")\n        srt_content.append(text)\n        srt_content.append(\"\")", "detail": "app", "documentation": {}}, {"label": "generate_dual_language_srt", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def generate_dual_language_srt(segments):\n    srt_content = []\n    for i, segment in enumerate(segments, 1):\n        start_time = format_timestamp(segment.get('start', 0))\n        end_time = format_timestamp(segment.get('end', 0))\n        chinese_text = segment.get('text', '').strip()\n        english_text = segment.get('translated', '').strip()\n        srt_content.append(f\"{i}\")\n        srt_content.append(f\"{start_time} --> {end_time}\")\n        srt_content.append(chinese_text)", "detail": "app", "documentation": {}}, {"label": "format_timestamp", "kind": 2, "importPath": "app", "description": "app", "peekOfCode": "def format_timestamp(seconds):\n    hours = int(seconds // 3600)\n    minutes = int((seconds % 3600) // 60)\n    seconds = seconds % 60\n    return f\"{hours:02d}:{minutes:02d}:{seconds:06.3f}\".replace('.', ',')\nif __name__ == '__main__':\n    print(\"\\n\" + \"=\"*50)\n    print(\"音频转文字应用\")\n    print(\"=\"*50)\n    print(f\"上传文件夹：{app.config['UPLOAD_FOLDER']}\")", "detail": "app", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "app = Flask(__name__)\napp.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE\napp.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\nos.makedirs(MODEL_DIR, exist_ok=True)\n# 显示设备信息\ngpu_info = print_device_info()", "detail": "app", "documentation": {}}, {"label": "app.config['MAX_CONTENT_LENGTH']", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "app.config['MAX_CONTENT_LENGTH'] = MAX_FILE_SIZE\napp.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\nos.makedirs(MODEL_DIR, exist_ok=True)\n# 显示设备信息\ngpu_info = print_device_info()\n# 配置Whisper模型", "detail": "app", "documentation": {}}, {"label": "app.config['UPLOAD_FOLDER']", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "app.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\nos.makedirs(MODEL_DIR, exist_ok=True)\n# 显示设备信息\ngpu_info = print_device_info()\n# 配置Whisper模型\nMODEL_NAME = DEFAULT_MODEL", "detail": "app", "documentation": {}}, {"label": "app.config['OUTPUT_FOLDER']", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "app.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\nos.makedirs(MODEL_DIR, exist_ok=True)\n# 显示设备信息\ngpu_info = print_device_info()\n# 配置Whisper模型\nMODEL_NAME = DEFAULT_MODEL\ndef load_whisper_model(model_name):", "detail": "app", "documentation": {}}, {"label": "gpu_info", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "gpu_info = print_device_info()\n# 配置Whisper模型\nMODEL_NAME = DEFAULT_MODEL\ndef load_whisper_model(model_name):\n    \"\"\"加载Whisper模型，优先使用本地模型\"\"\"\n    # 检查多个可能的模型位置\n    possible_paths = [\n        # 1. 标准whisper格式 (.pt文件)\n        os.path.join(MODEL_DIR, f\"{model_name}.pt\"),\n        os.path.join(MODEL_DIR, f\"{model_name}-v2.pt\"),", "detail": "app", "documentation": {}}, {"label": "MODEL_NAME", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "MODEL_NAME = DEFAULT_MODEL\ndef load_whisper_model(model_name):\n    \"\"\"加载Whisper模型，优先使用本地模型\"\"\"\n    # 检查多个可能的模型位置\n    possible_paths = [\n        # 1. 标准whisper格式 (.pt文件)\n        os.path.join(MODEL_DIR, f\"{model_name}.pt\"),\n        os.path.join(MODEL_DIR, f\"{model_name}-v2.pt\"),\n        # 2. Hugging Face格式 (pytorch_model.bin)\n        os.path.join(\"models\", f\"whisper-{model_name}\", \"pytorch_model.bin\"),", "detail": "app", "documentation": {}}, {"label": "model", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "model = load_whisper_model(MODEL_NAME)\n# 设置GPU/CPU设备\nmodel, device = setup_whisper_device(model)\nprint(\"Model loaded successfully!\")\ndef allowed_file(filename):\n    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS\***********('/')\ndef index():\n    return render_template('index.html')\***********('/upload', methods=['POST'])", "detail": "app", "documentation": {}}, {"label": "setup_local_model", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def setup_local_model():\n    \"\"\"设置本地模型，避免下载\"\"\"\n    local_hf_model = \"models/whisper-small/pytorch_model.bin\"\n    if os.path.exists(local_hf_model):\n        # 获取Whisper的缓存目录\n        cache_dir = pathlib.Path.home() / \".cache\" / \"whisper\"\n        cache_dir.mkdir(parents=True, exist_ok=True)\n        # 目标文件路径\n        cache_model_path = cache_dir / \"small.pt\"\n        # 如果缓存中没有模型，复制本地模型", "detail": "app_local", "documentation": {}}, {"label": "allowed_file", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def allowed_file(filename):\n    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS\***********('/')\ndef index():\n    return render_template('index.html')\***********('/upload', methods=['POST'])\ndef upload_file():\n    if 'file' not in request.files:\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']", "detail": "app_local", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def index():\n    return render_template('index.html')\***********('/upload', methods=['POST'])\ndef upload_file():\n    if 'file' not in request.files:\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']\n    if file.filename == '':\n        return jsonify({'error': 'No selected file'}), 400\n    if file and allowed_file(file.filename):", "detail": "app_local", "documentation": {}}, {"label": "upload_file", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def upload_file():\n    if 'file' not in request.files:\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']\n    if file.filename == '':\n        return jsonify({'error': 'No selected file'}), 400\n    if file and allowed_file(file.filename):\n        filename = secure_filename(file.filename)\n        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)\n        file.save(filepath)", "detail": "app_local", "documentation": {}}, {"label": "translate_text", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def translate_text():\n    data = request.get_json()\n    text = data.get('text', '')\n    target_lang = data.get('target', 'en')\n    if not text:\n        return jsonify({'error': 'No text provided'}), 400\n    try:\n        # 简单的翻译示例\n        translated_text = f\"[Translation to {target_lang}]: {text}\"\n        return jsonify({", "detail": "app_local", "documentation": {}}, {"label": "summarize_text", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def summarize_text():\n    data = request.get_json()\n    text = data.get('text', '')\n    if not text:\n        return jsonify({'error': 'No text provided'}), 400\n    try:\n        # 简单的总结示例\n        summary = f\"摘要：{text[:200]}...\" if len(text) > 200 else f\"摘要：{text}\"\n        return jsonify({\n            'success': True,", "detail": "app_local", "documentation": {}}, {"label": "generate_dual_subtitle", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def generate_dual_subtitle():\n    data = request.get_json()\n    segments = data.get('segments', [])\n    if not segments:\n        return jsonify({'error': 'No segments provided'}), 400\n    try:\n        # 生成双语字幕\n        dual_srt = generate_dual_language_srt(segments)\n        # 保存文件\n        filename = f\"dual_subtitle_{datetime.now().strftime('%Y%m%d_%H%M%S')}.srt\"", "detail": "app_local", "documentation": {}}, {"label": "download_file", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def download_file(filename):\n    try:\n        return send_file(\n            os.path.join(app.config['OUTPUT_FOLDER'], filename),\n            as_attachment=True,\n            download_name=filename\n        )\n    except Exception as e:\n        return jsonify({'error': str(e)}), 404\ndef generate_srt(transcription_result):", "detail": "app_local", "documentation": {}}, {"label": "generate_srt", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def generate_srt(transcription_result):\n    srt_content = []\n    for i, segment in enumerate(transcription_result['segments'], 1):\n        start_time = format_timestamp(segment['start'])\n        end_time = format_timestamp(segment['end'])\n        text = segment['text'].strip()\n        srt_content.append(f\"{i}\")\n        srt_content.append(f\"{start_time} --> {end_time}\")\n        srt_content.append(text)\n        srt_content.append(\"\")", "detail": "app_local", "documentation": {}}, {"label": "generate_dual_language_srt", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def generate_dual_language_srt(segments):\n    srt_content = []\n    for i, segment in enumerate(segments, 1):\n        start_time = format_timestamp(segment.get('start', 0))\n        end_time = format_timestamp(segment.get('end', 0))\n        chinese_text = segment.get('text', '').strip()\n        english_text = segment.get('translated', '').strip()\n        srt_content.append(f\"{i}\")\n        srt_content.append(f\"{start_time} --> {end_time}\")\n        srt_content.append(chinese_text)", "detail": "app_local", "documentation": {}}, {"label": "format_timestamp", "kind": 2, "importPath": "app_local", "description": "app_local", "peekOfCode": "def format_timestamp(seconds):\n    hours = int(seconds // 3600)\n    minutes = int((seconds % 3600) // 60)\n    seconds = seconds % 60\n    return f\"{hours:02d}:{minutes:02d}:{seconds:06.3f}\".replace('.', ',')\nif __name__ == '__main__':\n    print(\"\\n\" + \"=\"*50)\n    print(\"音频转文字应用\")\n    print(\"=\"*50)\n    print(f\"上传文件夹：{app.config['UPLOAD_FOLDER']}\")", "detail": "app_local", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "app = Flask(__name__)\napp.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size\napp.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\ndef setup_local_model():", "detail": "app_local", "documentation": {}}, {"label": "app.config['MAX_CONTENT_LENGTH']", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size\napp.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\ndef setup_local_model():\n    \"\"\"设置本地模型，避免下载\"\"\"", "detail": "app_local", "documentation": {}}, {"label": "app.config['UPLOAD_FOLDER']", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "app.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\ndef setup_local_model():\n    \"\"\"设置本地模型，避免下载\"\"\"\n    local_hf_model = \"models/whisper-small/pytorch_model.bin\"", "detail": "app_local", "documentation": {}}, {"label": "app.config['OUTPUT_FOLDER']", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "app.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\ndef setup_local_model():\n    \"\"\"设置本地模型，避免下载\"\"\"\n    local_hf_model = \"models/whisper-small/pytorch_model.bin\"\n    if os.path.exists(local_hf_model):", "detail": "app_local", "documentation": {}}, {"label": "ALLOWED_EXTENSIONS", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "ALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\ndef setup_local_model():\n    \"\"\"设置本地模型，避免下载\"\"\"\n    local_hf_model = \"models/whisper-small/pytorch_model.bin\"\n    if os.path.exists(local_hf_model):\n        # 获取Whisper的缓存目录\n        cache_dir = pathlib.Path.home() / \".cache\" / \"whisper\"\n        cache_dir.mkdir(parents=True, exist_ok=True)\n        # 目标文件路径\n        cache_model_path = cache_dir / \"small.pt\"", "detail": "app_local", "documentation": {}}, {"label": "gpu_info", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "gpu_info = print_device_info()\n# 设置本地模型\nprint(\"检查本地模型...\")\nmodel_ready = setup_local_model()\nif not model_ready:\n    print(\"警告：无法设置本地模型，Whisper可能会尝试下载\")\n    print(\"提示：确保模型文件在 models/whisper-small/pytorch_model.bin\")\n# 加载Whisper模型\nprint(\"Loading Whisper model...\")\ntry:", "detail": "app_local", "documentation": {}}, {"label": "model_ready", "kind": 5, "importPath": "app_local", "description": "app_local", "peekOfCode": "model_ready = setup_local_model()\nif not model_ready:\n    print(\"警告：无法设置本地模型，Whisper可能会尝试下载\")\n    print(\"提示：确保模型文件在 models/whisper-small/pytorch_model.bin\")\n# 加载Whisper模型\nprint(\"Loading Whisper model...\")\ntry:\n    # 如果设置成功，这里不会触发下载\n    model = whisper.load_model(\"small\")\n    # 设置GPU/CPU设备", "detail": "app_local", "documentation": {}}, {"label": "allowed_file", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def allowed_file(filename):\n    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS\ndef call_ai_api(prompt, text, temperature=None):\n    \"\"\"调用AI API\"\"\"\n    try:\n        headers = {\n            \"Authorization\": f\"Bearer {AI_CONFIG['api_key']}\",\n            \"Content-Type\": \"application/json\"\n        }\n        data = {", "detail": "app_with_ai", "documentation": {}}, {"label": "call_ai_api", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def call_ai_api(prompt, text, temperature=None):\n    \"\"\"调用AI API\"\"\"\n    try:\n        headers = {\n            \"Authorization\": f\"Bearer {AI_CONFIG['api_key']}\",\n            \"Content-Type\": \"application/json\"\n        }\n        data = {\n            \"model\": AI_CONFIG['model'],\n            \"messages\": [", "detail": "app_with_ai", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def index():\n    return render_template('index_ai.html')\***********('/upload', methods=['POST'])\ndef upload_file():\n    print(f\"[Upload] Request received at {datetime.now()}\")\n    if 'file' not in request.files:\n        print(\"[Upload] Error: No file part in request\")\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']\n    if file.filename == '':", "detail": "app_with_ai", "documentation": {}}, {"label": "upload_file", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def upload_file():\n    print(f\"[Upload] Request received at {datetime.now()}\")\n    if 'file' not in request.files:\n        print(\"[Upload] Error: No file part in request\")\n        return jsonify({'error': 'No file part'}), 400\n    file = request.files['file']\n    if file.filename == '':\n        print(\"[Upload] Error: No selected file\")\n        return jsonify({'error': 'No selected file'}), 400\n    if file and allowed_file(file.filename):", "detail": "app_with_ai", "documentation": {}}, {"label": "ai_process", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def ai_process():\n    \"\"\"使用AI处理文本\"\"\"\n    data = request.get_json()\n    text = data.get('text', '')\n    tool_type = data.get('tool_type', 'summarize')\n    custom_prompt = data.get('custom_prompt', '')\n    if not text:\n        return jsonify({'error': ERROR_MESSAGES['empty_text']}), 400\n    # 获取提示语\n    if tool_type == 'custom':", "detail": "app_with_ai", "documentation": {}}, {"label": "get_ai_tools", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def get_ai_tools():\n    \"\"\"获取可用的AI工具列表\"\"\"\n    tools = []\n    for key, value in AI_PROMPTS.items():\n        tools.append({\n            'id': key,\n            'name': value['name'],\n            'description': value['description']\n        })\n    return jsonify({'tools': tools})", "detail": "app_with_ai", "documentation": {}}, {"label": "translate_text", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def translate_text():\n    \"\"\"翻译文本\"\"\"\n    data = request.get_json()\n    text = data.get('text', '')\n    target_lang = data.get('target', 'en')\n    if not text:\n        return jsonify({'error': 'No text provided'}), 400\n    # 构建翻译提示语\n    lang_map = {\n        'en': '英文',", "detail": "app_with_ai", "documentation": {}}, {"label": "generate_dual_subtitle", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def generate_dual_subtitle():\n    \"\"\"生成双语字幕\"\"\"\n    data = request.get_json()\n    segments = data.get('segments', [])\n    target_lang = data.get('target_lang', 'en')\n    print(f\"\\n[DEBUG] 接收到的segments数量: {len(segments)}\")\n    print(f\"[DEBUG] 目标语言: {target_lang}\")\n    if segments:\n        print(f\"[DEBUG] 第一个segment内容: {segments[0]}\")\n    if not segments:", "detail": "app_with_ai", "documentation": {}}, {"label": "download_file", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def download_file(filename):\n    try:\n        return send_file(\n            os.path.join(app.config['OUTPUT_FOLDER'], filename),\n            as_attachment=True,\n            download_name=filename\n        )\n    except Exception as e:\n        return jsonify({'error': str(e)}), 404\ndef generate_srt(transcription_result):", "detail": "app_with_ai", "documentation": {}}, {"label": "generate_srt", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def generate_srt(transcription_result):\n    srt_content = []\n    for i, segment in enumerate(transcription_result['segments'], 1):\n        start_time = format_timestamp(segment['start'])\n        end_time = format_timestamp(segment['end'])\n        text = segment['text'].strip()\n        srt_content.append(f\"{i}\")\n        srt_content.append(f\"{start_time} --> {end_time}\")\n        srt_content.append(text)\n        srt_content.append(\"\")", "detail": "app_with_ai", "documentation": {}}, {"label": "generate_dual_language_srt", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def generate_dual_language_srt(segments):\n    srt_content = []\n    print(f\"\\n[DEBUG generate_dual_language_srt] 处理{len(segments)}个segments\")\n    for i, segment in enumerate(segments, 1):\n        start_time = format_timestamp(segment.get('start', 0))\n        end_time = format_timestamp(segment.get('end', 0))\n        original_text = segment.get('text', '').strip()\n        translated_text = segment.get('translated', '').strip()\n        print(f\"[DEBUG] Segment {i}:\")\n        print(f\"  - 原文: {original_text}\")", "detail": "app_with_ai", "documentation": {}}, {"label": "format_timestamp", "kind": 2, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "def format_timestamp(seconds):\n    hours = int(seconds // 3600)\n    minutes = int((seconds % 3600) // 60)\n    seconds = seconds % 60\n    return f\"{hours:02d}:{minutes:02d}:{seconds:06.3f}\".replace('.', ',')\nif __name__ == '__main__':\n    print(\"\\n\" + \"=\"*50)\n    print(\"音频转文字应用 (集成AI工具)\")\n    print(\"=\"*50)\n    print(f\"AI模型：{AI_CONFIG['model']}\")", "detail": "app_with_ai", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "app = Flask(__name__)\napp.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size\napp.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\n# 显示设备信息", "detail": "app_with_ai", "documentation": {}}, {"label": "app.config['MAX_CONTENT_LENGTH']", "kind": 5, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size\napp.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\n# 显示设备信息\ngpu_info = print_device_info()", "detail": "app_with_ai", "documentation": {}}, {"label": "app.config['UPLOAD_FOLDER']", "kind": 5, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "app.config['UPLOAD_FOLDER'] = 'uploads'\napp.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\n# 显示设备信息\ngpu_info = print_device_info()\n# 加载Whisper模型", "detail": "app_with_ai", "documentation": {}}, {"label": "app.config['OUTPUT_FOLDER']", "kind": 5, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "app.config['OUTPUT_FOLDER'] = 'outputs'\n# 创建必要的文件夹\nos.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)\nos.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)\n# 允许的文件扩展名\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\n# 显示设备信息\ngpu_info = print_device_info()\n# 加载Whisper模型\ntry:", "detail": "app_with_ai", "documentation": {}}, {"label": "ALLOWED_EXTENSIONS", "kind": 5, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "ALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}\n# 显示设备信息\ngpu_info = print_device_info()\n# 加载Whisper模型\ntry:\n    model, device = safe_load_whisper_model(\"small\")\nexcept:\n    print(\"Failed to load small model, falling back to base...\")\n    try:\n        model, device = safe_load_whisper_model(\"base\")", "detail": "app_with_ai", "documentation": {}}, {"label": "gpu_info", "kind": 5, "importPath": "app_with_ai", "description": "app_with_ai", "peekOfCode": "gpu_info = print_device_info()\n# 加载Whisper模型\ntry:\n    model, device = safe_load_whisper_model(\"small\")\nexcept:\n    print(\"Failed to load small model, falling back to base...\")\n    try:\n        model, device = safe_load_whisper_model(\"base\")\n    except Exception as e:\n        print(f\"Failed to load any model: {e}\")", "detail": "app_with_ai", "documentation": {}}, {"label": "lines", "kind": 5, "importPath": "clean_translation_fix", "description": "clean_translation_fix", "peekOfCode": "lines = content.split('\\n')\nnew_lines = []\nskip_until = -1\nfor i, line in enumerate(lines):\n    if skip_until > 0 and i < skip_until:\n        continue\n    # 找到第一个重复的映射部分（line 381附近）\n    if i >= 380 and i <= 385 and 'print(\"[DEBUG] translations未定义\")' in line:\n        # 跳过这部分直到找到正确的映射代码开始\n        for j in range(i, min(i+15, len(lines))):", "detail": "clean_translation_fix", "documentation": {}}, {"label": "new_lines", "kind": 5, "importPath": "clean_translation_fix", "description": "clean_translation_fix", "peekOfCode": "new_lines = []\nskip_until = -1\nfor i, line in enumerate(lines):\n    if skip_until > 0 and i < skip_until:\n        continue\n    # 找到第一个重复的映射部分（line 381附近）\n    if i >= 380 and i <= 385 and 'print(\"[DEBUG] translations未定义\")' in line:\n        # 跳过这部分直到找到正确的映射代码开始\n        for j in range(i, min(i+15, len(lines))):\n            if '# 映射翻译结果到segments（批量处理）' in lines[j]:", "detail": "clean_translation_fix", "documentation": {}}, {"label": "skip_until", "kind": 5, "importPath": "clean_translation_fix", "description": "clean_translation_fix", "peekOfCode": "skip_until = -1\nfor i, line in enumerate(lines):\n    if skip_until > 0 and i < skip_until:\n        continue\n    # 找到第一个重复的映射部分（line 381附近）\n    if i >= 380 and i <= 385 and 'print(\"[DEBUG] translations未定义\")' in line:\n        # 跳过这部分直到找到正确的映射代码开始\n        for j in range(i, min(i+15, len(lines))):\n            if '# 映射翻译结果到segments（批量处理）' in lines[j]:\n                skip_until = j", "detail": "clean_translation_fix", "documentation": {}}, {"label": "content", "kind": 5, "importPath": "clean_translation_fix", "description": "clean_translation_fix", "peekOfCode": "content = '\\n'.join(new_lines)\n# 3. 修复单批处理部分的else缩进问题（line 486附近）\n# 查找错误缩进的else并修复\ncontent = content.replace(\n    '                            print(f\"[DEBUG] segment[{idx}]没有找到翻译（索引{i}），使用原文\")\\n                    \\n                            else:',\n    '                            print(f\"[DEBUG] segment[{idx}]没有找到翻译（索引{i}），使用原文\")\\n                    else:'\n)\n# 4. 确保AI调用失败处理的正确缩进\ncontent = content.replace(\n    '                    else:\\n                        # AI调用失败，使用原文作为翻译',", "detail": "clean_translation_fix", "documentation": {}}, {"label": "content", "kind": 5, "importPath": "clean_translation_fix", "description": "clean_translation_fix", "peekOfCode": "content = content.replace(\n    '                            print(f\"[DEBUG] segment[{idx}]没有找到翻译（索引{i}），使用原文\")\\n                    \\n                            else:',\n    '                            print(f\"[DEBUG] segment[{idx}]没有找到翻译（索引{i}），使用原文\")\\n                    else:'\n)\n# 4. 确保AI调用失败处理的正确缩进\ncontent = content.replace(\n    '                    else:\\n                        # AI调用失败，使用原文作为翻译',\n    '                else:\\n                    # AI调用失败，使用原文作为翻译'\n)\n# 保存文件", "detail": "clean_translation_fix", "documentation": {}}, {"label": "content", "kind": 5, "importPath": "clean_translation_fix", "description": "clean_translation_fix", "peekOfCode": "content = content.replace(\n    '                    else:\\n                        # AI调用失败，使用原文作为翻译',\n    '                else:\\n                    # AI调用失败，使用原文作为翻译'\n)\n# 保存文件\nwith open('app_with_ai.py', 'w', encoding='utf-8') as f:\n    f.write(content)\nprint(\"翻译逻辑清理完成！\")\nprint(\"- 删除了重复的映射代码\")  \nprint(\"- 修复了缩进问题\")", "detail": "clean_translation_fix", "documentation": {}}, {"label": "WHISPER_MODELS", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "WHISPER_MODELS = {\n    \"tiny\": {\n        \"description\": \"最小最快的模型 (~39MB)\",\n        \"accuracy\": \"★★☆☆☆\",\n        \"speed\": \"★★★★★\",\n        \"memory\": \"~1GB VRAM\",\n        \"use_case\": \"快速测试、低质量音频\"\n    },\n    \"base\": {\n        \"description\": \"平衡的基础模型 (~74MB)\",", "detail": "config", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "DEFAULT_MODEL = os.environ.get('WHISPER_MODEL', 'small')\n# 模型存储路径\nMODEL_DIR = os.path.join(os.path.dirname(__file__), \"models\", \"whisper\")\n# 支持的音频格式\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac', 'flac', 'ogg'}\n# 文件大小限制\nMAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB\n# 语言设置\nSUPPORTED_LANGUAGES = {\n    'zh': '中文',", "detail": "config", "documentation": {}}, {"label": "MODEL_DIR", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "MODEL_DIR = os.path.join(os.path.dirname(__file__), \"models\", \"whisper\")\n# 支持的音频格式\nALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac', 'flac', 'ogg'}\n# 文件大小限制\nMAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB\n# 语言设置\nSUPPORTED_LANGUAGES = {\n    'zh': '中文',\n    'en': '英语',\n    'ja': '日语',", "detail": "config", "documentation": {}}, {"label": "ALLOWED_EXTENSIONS", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "ALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac', 'flac', 'ogg'}\n# 文件大小限制\nMAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB\n# 语言设置\nSUPPORTED_LANGUAGES = {\n    'zh': '中文',\n    'en': '英语',\n    'ja': '日语',\n    'ko': '韩语',\n    'es': '西班牙语',", "detail": "config", "documentation": {}}, {"label": "MAX_FILE_SIZE", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB\n# 语言设置\nSUPPORTED_LANGUAGES = {\n    'zh': '中文',\n    'en': '英语',\n    'ja': '日语',\n    'ko': '韩语',\n    'es': '西班牙语',\n    'fr': '法语',\n    'de': '德语',", "detail": "config", "documentation": {}}, {"label": "SUPPORTED_LANGUAGES", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "SUPPORTED_LANGUAGES = {\n    'zh': '中文',\n    'en': '英语',\n    'ja': '日语',\n    'ko': '韩语',\n    'es': '西班牙语',\n    'fr': '法语',\n    'de': '德语',\n    'ru': '俄语',\n    'ar': '阿拉伯语',", "detail": "config", "documentation": {}}, {"label": "DEFAULT_LANGUAGE", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "DEFAULT_LANGUAGE = 'zh'", "detail": "config", "documentation": {}}, {"label": "check_upload_directory", "kind": 2, "importPath": "debug_upload", "description": "debug_upload", "peekOfCode": "def check_upload_directory():\n    \"\"\"Check if upload directory exists and is writable\"\"\"\n    upload_dir = 'uploads'\n    print(f\"Checking upload directory: {upload_dir}\")\n    if os.path.exists(upload_dir):\n        print(f\"✓ Directory exists\")\n        # Check if writable\n        test_file = os.path.join(upload_dir, 'test.txt')\n        try:\n            with open(test_file, 'w') as f:", "detail": "debug_upload", "documentation": {}}, {"label": "check_disk_space", "kind": 2, "importPath": "debug_upload", "description": "debug_upload", "peekOfCode": "def check_disk_space():\n    \"\"\"Check available disk space\"\"\"\n    import shutil\n    print(\"\\nChecking disk space:\")\n    try:\n        stat = shutil.disk_usage('.')\n        print(f\"Total: {stat.total / (1024**3):.2f} GB\")\n        print(f\"Free: {stat.free / (1024**3):.2f} GB\")\n        print(f\"Used: {(stat.used / stat.total) * 100:.1f}%\")\n    except Exception as e:", "detail": "debug_upload", "documentation": {}}, {"label": "check_flask_app", "kind": 2, "importPath": "debug_upload", "description": "debug_upload", "peekOfCode": "def check_flask_app():\n    \"\"\"Check if Flask app can be imported\"\"\"\n    print(\"\\nChecking Flask app:\")\n    try:\n        import app_with_ai\n        print(\"✓ app_with_ai.py can be imported\")\n        # Check if upload folder is configured\n        if hasattr(app_with_ai.app, 'config'):\n            print(f\"✓ Upload folder: {app_with_ai.app.config.get('UPLOAD_FOLDER', 'Not set')}\")\n            print(f\"✓ Max content length: {app_with_ai.app.config.get('MAX_CONTENT_LENGTH', 'Not set')} bytes\")", "detail": "debug_upload", "documentation": {}}, {"label": "download_with_resume", "kind": 2, "importPath": "download_whisper_simple", "description": "download_whisper_simple", "peekOfCode": "def download_with_resume(url, dest_path, expected_size_mb=None):\n    \"\"\"支持断点续传的下载函数\"\"\"\n    headers = {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n    }\n    # 获取已下载的大小\n    resume_pos = 0\n    mode = 'wb'\n    if os.path.exists(dest_path):\n        resume_pos = os.path.getsize(dest_path)", "detail": "download_whisper_simple", "documentation": {}}, {"label": "download_model", "kind": 2, "importPath": "download_whisper_simple", "description": "download_whisper_simple", "peekOfCode": "def download_model(model_name):\n    \"\"\"下载指定的Whisper模型\"\"\"\n    if model_name not in WHISPER_MODELS:\n        print(f\"错误：不支持的模型 '{model_name}'\")\n        print(f\"支持的模型：{', '.join(WHISPER_MODELS.keys())}\")\n        return False\n    model_info = WHISPER_MODELS[model_name]\n    # 创建模型目录\n    model_dir = os.path.join(\"models\", \"whisper\")\n    os.makedirs(model_dir, exist_ok=True)", "detail": "download_whisper_simple", "documentation": {}}, {"label": "list_models", "kind": 2, "importPath": "download_whisper_simple", "description": "download_whisper_simple", "peekOfCode": "def list_models():\n    \"\"\"列出所有可用和已下载的模型\"\"\"\n    model_dir = os.path.join(\"models\", \"whisper\")\n    print(\"\\n=== 可用的Whisper模型 ===\")\n    print(\"\\n多语言模型：\")\n    for name, info in WHISPER_MODELS.items():\n        if not name.endswith('.en') and name not in ['large-v1', 'large-v2']:\n            print(f\"  {name:8} - {info['size_mb']:4}MB - {info['filename']}\")\n    print(\"\\n仅英语模型（速度更快）：\")\n    for name, info in WHISPER_MODELS.items():", "detail": "download_whisper_simple", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "download_whisper_simple", "description": "download_whisper_simple", "peekOfCode": "def main():\n    if len(sys.argv) < 2:\n        print(\"Whisper模型下载工具（使用Hugging Face镜像）\")\n        print(\"\\n使用方法：\")\n        print(f\"  python {os.path.basename(sys.argv[0])} <model>  # 下载指定模型\")\n        print(f\"  python {os.path.basename(sys.argv[0])} list    # 查看所有模型\")\n        print(\"\\n示例：\")\n        print(f\"  python {os.path.basename(sys.argv[0])} base    # 下载base模型（推荐）\")\n        print(f\"  python {os.path.basename(sys.argv[0])} tiny    # 下载最小的模型\")\n        print(f\"  python {os.path.basename(sys.argv[0])} large   # 下载最大的模型\")", "detail": "download_whisper_simple", "documentation": {}}, {"label": "WHISPER_MODELS", "kind": 5, "importPath": "download_whisper_simple", "description": "download_whisper_simple", "peekOfCode": "WHISPER_MODELS = {\n    \"tiny\": {\n        \"url\": \"https://hf-mirror.com/openai/whisper-tiny/resolve/main/pytorch_model.bin\",\n        \"size_mb\": 39,\n        \"filename\": \"tiny.pt\"\n    },\n    \"tiny.en\": {\n        \"url\": \"https://hf-mirror.com/openai/whisper-tiny.en/resolve/main/pytorch_model.bin\", \n        \"size_mb\": 39,\n        \"filename\": \"tiny.en.pt\"", "detail": "download_whisper_simple", "documentation": {}}, {"label": "old_code", "kind": 5, "importPath": "final_fix_translation", "description": "final_fix_translation", "peekOfCode": "old_code = '''                # 映射翻译结果到segments（批量和单批共用）\n                print(f\"\\\\n[DEBUG] 开始映射翻译结果到segments\")\n                if \"translations\" in locals():\n                    print(f\"[DEBUG] translations字典包含 {len(translations)} 个翻译\")\n                else:\n                    print(\"[DEBUG] translations未定义\")\n                    translations = {}\n                print(f\"[DEBUG] valid_indices前10个: {valid_indices[:10]}...\")\n                # 映射翻译结果\n                for i, idx in enumerate(valid_indices):", "detail": "final_fix_translation", "documentation": {}}, {"label": "new_code", "kind": 5, "importPath": "final_fix_translation", "description": "final_fix_translation", "peekOfCode": "new_code = '''                else:'''\ncontent = content.replace(old_code, new_code)\n# 保存文件\nwith open('app_with_ai.py', 'w', encoding='utf-8') as f:\n    f.write(content)\nprint(\"修复完成！\")", "detail": "final_fix_translation", "documentation": {}}, {"label": "content", "kind": 5, "importPath": "final_fix_translation", "description": "final_fix_translation", "peekOfCode": "content = content.replace(old_code, new_code)\n# 保存文件\nwith open('app_with_ai.py', 'w', encoding='utf-8') as f:\n    f.write(content)\nprint(\"修复完成！\")", "detail": "final_fix_translation", "documentation": {}}, {"label": "new_lines", "kind": 5, "importPath": "final_translation_fix", "description": "final_translation_fix", "peekOfCode": "new_lines = []\nin_mapping_section = False\nskip_line = False\nbatch_section_found = False\nfor i, line in enumerate(lines):\n    # 跳过错误位置的映射代码\n    if '# 映射翻译结果到segments（批量和单批共用）' in line and i < 400:\n        in_mapping_section = True\n        continue\n    if in_mapping_section:", "detail": "final_translation_fix", "documentation": {}}, {"label": "in_mapping_section", "kind": 5, "importPath": "final_translation_fix", "description": "final_translation_fix", "peekOfCode": "in_mapping_section = False\nskip_line = False\nbatch_section_found = False\nfor i, line in enumerate(lines):\n    # 跳过错误位置的映射代码\n    if '# 映射翻译结果到segments（批量和单批共用）' in line and i < 400:\n        in_mapping_section = True\n        continue\n    if in_mapping_section:\n        # 继续跳过，直到找到else:", "detail": "final_translation_fix", "documentation": {}}, {"label": "skip_line", "kind": 5, "importPath": "final_translation_fix", "description": "final_translation_fix", "peekOfCode": "skip_line = False\nbatch_section_found = False\nfor i, line in enumerate(lines):\n    # 跳过错误位置的映射代码\n    if '# 映射翻译结果到segments（批量和单批共用）' in line and i < 400:\n        in_mapping_section = True\n        continue\n    if in_mapping_section:\n        # 继续跳过，直到找到else:\n        if line.strip() == 'else:' and i > 380 and i < 410:", "detail": "final_translation_fix", "documentation": {}}, {"label": "batch_section_found", "kind": 5, "importPath": "final_translation_fix", "description": "final_translation_fix", "peekOfCode": "batch_section_found = False\nfor i, line in enumerate(lines):\n    # 跳过错误位置的映射代码\n    if '# 映射翻译结果到segments（批量和单批共用）' in line and i < 400:\n        in_mapping_section = True\n        continue\n    if in_mapping_section:\n        # 继续跳过，直到找到else:\n        if line.strip() == 'else:' and i > 380 and i < 410:\n            in_mapping_section = False", "detail": "final_translation_fix", "documentation": {}}, {"label": "run_command", "kind": 2, "importPath": "fix_gpu", "description": "fix_gpu", "peekOfCode": "def run_command(cmd, description):\n    \"\"\"运行命令并显示结果\"\"\"\n    print(f\"\\n{description}...\")\n    try:\n        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)\n        if result.returncode == 0:\n            print(f\"✓ {description}成功\")\n            if result.stdout:\n                print(result.stdout)\n            return True", "detail": "fix_gpu", "documentation": {}}, {"label": "cuda_install_cmd", "kind": 5, "importPath": "fix_gpu", "description": "fix_gpu", "peekOfCode": "cuda_install_cmd = \"pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\"\nif not run_command(cuda_install_cmd, \"安装CUDA版本PyTorch\"):\n    print(\"\\n尝试使用备选方案...\")\n    # 备选方案1：使用清华镜像\n    print(\"\\n尝试使用清华镜像源...\")\n    mirror_cmd = \"pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple\"\n    if not run_command(mirror_cmd, \"通过镜像安装\"):\n        print(\"\\n安装失败！可能的解决方案：\")\n        print(\"1. 检查网络连接\")\n        print(\"2. 手动下载whl文件：\")", "detail": "fix_gpu", "documentation": {}}, {"label": "response", "kind": 5, "importPath": "fix_gpu", "description": "fix_gpu", "peekOfCode": "response = input(\"\\n是否运行完整的GPU诊断？(y/n): \")\nif response.lower() == 'y':\n    subprocess.run([sys.executable, \"check_gpu.py\"])", "detail": "fix_gpu", "documentation": {}}, {"label": "improved_generate_dual_subtitle", "kind": 2, "importPath": "fix_translation", "description": "fix_translation", "peekOfCode": "def improved_generate_dual_subtitle(segments, target_lang='en'):\n    \"\"\"改进的双语字幕生成函数\"\"\"\n    import copy\n    # 深拷贝segments以避免修改原始数据\n    translated_segments = copy.deepcopy(segments)\n    # 提取需要翻译的文本\n    texts_to_translate = []\n    valid_indices = []\n    for i, segment in enumerate(segments):\n        text = segment.get('text', '').strip()", "detail": "fix_translation", "documentation": {}}, {"label": "batch_translate", "kind": 2, "importPath": "fix_translation", "description": "fix_translation", "peekOfCode": "def batch_translate(texts, target_lang):\n    \"\"\"批量翻译文本 - 更健壮的版本\"\"\"\n    # 构建请求\n    combined_text = \"\\n\".join([f\"[{i+1}] {text}\" for i, text in enumerate(texts)])\n    # 调用AI\n    result = call_ai_with_fallback(combined_text, target_lang)\n    # 解析结果 - 更健壮的解析\n    translations = []\n    if result['success']:\n        content = result['content']", "detail": "fix_translation", "documentation": {}}, {"label": "call_ai_with_fallback", "kind": 2, "importPath": "fix_translation", "description": "fix_translation", "peekOfCode": "def call_ai_with_fallback(combined_text, target_lang):\n    \"\"\"带有重试和fallback的AI调用\"\"\"\n    # 第一次尝试\n    result = call_ai_api(prompt, combined_text, temperature=0.3)\n    if not result['success']:\n        # 如果失败，尝试更简单的prompt\n        simple_prompt = f\"Translate to {target_lang}:\\n{{text}}\"\n        result = call_ai_api(simple_prompt, combined_text, temperature=0.3)\n    return result\n# 另一个可能的问题：确保正确处理空segments", "detail": "fix_translation", "documentation": {}}, {"label": "sanitize_segments", "kind": 2, "importPath": "fix_translation", "description": "fix_translation", "peekOfCode": "def sanitize_segments(segments):\n    \"\"\"清理和验证segments\"\"\"\n    clean_segments = []\n    for segment in segments:\n        if isinstance(segment, dict) and 'text' in segment:\n            # 确保必要的字段存在\n            clean_segment = {\n                'start': segment.get('start', 0),\n                'end': segment.get('end', 0),\n                'text': segment.get('text', '').strip()", "detail": "fix_translation", "documentation": {}}, {"label": "pattern1", "kind": 5, "importPath": "fix_translation_complete", "description": "fix_translation_complete", "peekOfCode": "pattern1 = r'(\\s+# 映射翻译结果到segments（批量和单批共用）[\\s\\S]*?print\\(f\"DEBUG\\] segment\\[{idx}\\]没有找到翻译（索引{i}）\"\\)\\s*\\n\\s*else:)'\n# 替换为简单的else:\ncontent = re.sub(pattern1, r'\\n                else:', content, flags=re.MULTILINE)\n# 现在在单批处理的映射代码后，但在最外层的else之前，添加完整的映射逻辑\n# 找到单批处理映射代码的结束位置\npattern2 = r'(# 如果没有解析到任何翻译，尝试简单的逐行对应[\\s\\S]*?print\\(f\"DEBUG\\] 逐行对应 - 索引{i}: {translations\\[i\\]}\"\\)\\s*\\n)(\\s+else:)'\n# 在这个位置插入映射代码\nreplacement = r'''\\1\n                # 映射翻译结果到segments\n                print(f\"\\n[DEBUG] 开始映射翻译结果到segments\")", "detail": "fix_translation_complete", "documentation": {}}, {"label": "content", "kind": 5, "importPath": "fix_translation_complete", "description": "fix_translation_complete", "peekOfCode": "content = re.sub(pattern1, r'\\n                else:', content, flags=re.MULTILINE)\n# 现在在单批处理的映射代码后，但在最外层的else之前，添加完整的映射逻辑\n# 找到单批处理映射代码的结束位置\npattern2 = r'(# 如果没有解析到任何翻译，尝试简单的逐行对应[\\s\\S]*?print\\(f\"DEBUG\\] 逐行对应 - 索引{i}: {translations\\[i\\]}\"\\)\\s*\\n)(\\s+else:)'\n# 在这个位置插入映射代码\nreplacement = r'''\\1\n                # 映射翻译结果到segments\n                print(f\"\\n[DEBUG] 开始映射翻译结果到segments\")\n                print(f\"[DEBUG] translations字典包含 {len(translations)} 个翻译\")\n                print(f\"[DEBUG] valid_indices前10个: {valid_indices[:10]}...\")", "detail": "fix_translation_complete", "documentation": {}}, {"label": "pattern2", "kind": 5, "importPath": "fix_translation_complete", "description": "fix_translation_complete", "peekOfCode": "pattern2 = r'(# 如果没有解析到任何翻译，尝试简单的逐行对应[\\s\\S]*?print\\(f\"DEBUG\\] 逐行对应 - 索引{i}: {translations\\[i\\]}\"\\)\\s*\\n)(\\s+else:)'\n# 在这个位置插入映射代码\nreplacement = r'''\\1\n                # 映射翻译结果到segments\n                print(f\"\\n[DEBUG] 开始映射翻译结果到segments\")\n                print(f\"[DEBUG] translations字典包含 {len(translations)} 个翻译\")\n                print(f\"[DEBUG] valid_indices前10个: {valid_indices[:10]}...\")\n                # 映射翻译结果\n                for i, idx in enumerate(valid_indices):\n                    if i in translations:", "detail": "fix_translation_complete", "documentation": {}}, {"label": "replacement", "kind": 5, "importPath": "fix_translation_complete", "description": "fix_translation_complete", "peekOfCode": "replacement = r'''\\1\n                # 映射翻译结果到segments\n                print(f\"\\n[DEBUG] 开始映射翻译结果到segments\")\n                print(f\"[DEBUG] translations字典包含 {len(translations)} 个翻译\")\n                print(f\"[DEBUG] valid_indices前10个: {valid_indices[:10]}...\")\n                # 映射翻译结果\n                for i, idx in enumerate(valid_indices):\n                    if i in translations:\n                        translated_segments[idx] = {\n                            **segments[idx],", "detail": "fix_translation_complete", "documentation": {}}, {"label": "content", "kind": 5, "importPath": "fix_translation_complete", "description": "fix_translation_complete", "peekOfCode": "content = re.sub(pattern2, replacement, content, flags=re.MULTILINE)\n# 保存文件\nwith open('app_with_ai.py', 'w', encoding='utf-8') as f:\n    f.write(content)\nprint(\"修复完成！翻译映射逻辑已正确重构。\")", "detail": "fix_translation_complete", "documentation": {}}, {"label": "new_lines", "kind": 5, "importPath": "fix_translation_mapping", "description": "fix_translation_mapping", "peekOfCode": "new_lines = []\nskip_until = -1\nfor i, line in enumerate(lines):\n    if skip_until > 0 and i < skip_until:\n        continue\n    # 找到批量翻译完成的地方\n    if 'print(f\"[DEBUG] 批量翻译完成，共翻译 {len(translations)} 个文本\")' in line:\n        new_lines.append(line)\n        # 在这之后添加映射代码\n        new_lines.append('\\n')", "detail": "fix_translation_mapping", "documentation": {}}, {"label": "skip_until", "kind": 5, "importPath": "fix_translation_mapping", "description": "fix_translation_mapping", "peekOfCode": "skip_until = -1\nfor i, line in enumerate(lines):\n    if skip_until > 0 and i < skip_until:\n        continue\n    # 找到批量翻译完成的地方\n    if 'print(f\"[DEBUG] 批量翻译完成，共翻译 {len(translations)} 个文本\")' in line:\n        new_lines.append(line)\n        # 在这之后添加映射代码\n        new_lines.append('\\n')\n        new_lines.append('                # 映射翻译结果到segments（批量和单批共用）\\n')", "detail": "fix_translation_mapping", "documentation": {}}, {"label": "get_gpu_info", "kind": 2, "importPath": "gpu_utils", "description": "gpu_utils", "peekOfCode": "def get_gpu_info():\n    \"\"\"获取GPU信息\"\"\"\n    gpu_info = {\n        'available': False,\n        'cuda_available': False,\n        'device_count': 0,\n        'device_name': 'CPU',\n        'device_type': 'cpu',\n        'memory_allocated': 0,\n        'memory_reserved': 0,", "detail": "gpu_utils", "documentation": {}}, {"label": "get_optimal_device", "kind": 2, "importPath": "gpu_utils", "description": "gpu_utils", "peekOfCode": "def get_optimal_device():\n    \"\"\"获取最优设备（优先GPU）\"\"\"\n    if torch.cuda.is_available():\n        device = 'cuda'\n        print(\"✓ 使用GPU加速\")\n        # 清理GPU缓存\n        torch.cuda.empty_cache()\n    else:\n        device = 'cpu'\n        print(\"→ 使用CPU处理（未检测到可用GPU）\")", "detail": "gpu_utils", "documentation": {}}, {"label": "print_device_info", "kind": 2, "importPath": "gpu_utils", "description": "gpu_utils", "peekOfCode": "def print_device_info():\n    \"\"\"打印设备信息\"\"\"\n    gpu_info = get_gpu_info()\n    print(\"\\n=== 设备信息 ===\")\n    if gpu_info['cuda_available']:\n        print(f\"GPU状态: 可用\")\n        print(f\"GPU型号: {gpu_info['device_name']}\")\n        print(f\"GPU数量: {gpu_info['device_count']}\")\n        print(f\"CUDA版本: {gpu_info['cuda_version']}\")\n        if gpu_info['driver_version']:", "detail": "gpu_utils", "documentation": {}}, {"label": "setup_whisper_device", "kind": 2, "importPath": "gpu_utils", "description": "gpu_utils", "peekOfCode": "def setup_whisper_device(model):\n    \"\"\"为Whisper模型设置最优设备\"\"\"\n    device = get_optimal_device()\n    # 统一使用float32以避免兼容性问题\n    model = model.float()\n    # 如果使用GPU，确保模型在GPU上\n    if device == 'cuda':\n        model = model.cuda()\n        print(\"→ GPU模式：使用FP32精度\")\n    else:", "detail": "gpu_utils", "documentation": {}}, {"label": "monitor_gpu_memory", "kind": 2, "importPath": "gpu_utils", "description": "gpu_utils", "peekOfCode": "def monitor_gpu_memory():\n    \"\"\"监控GPU内存使用\"\"\"\n    if torch.cuda.is_available():\n        allocated = torch.cuda.memory_allocated(0) / 1024 / 1024  # MB\n        reserved = torch.cuda.memory_reserved(0) / 1024 / 1024    # MB\n        return {\n            'allocated': allocated,\n            'reserved': reserved,\n            'free': reserved - allocated\n        }", "detail": "gpu_utils", "documentation": {}}, {"label": "clear_gpu_cache", "kind": 2, "importPath": "gpu_utils", "description": "gpu_utils", "peekOfCode": "def clear_gpu_cache():\n    \"\"\"清理GPU缓存\"\"\"\n    if torch.cuda.is_available():\n        torch.cuda.empty_cache()\n        print(\"→ GPU缓存已清理\")", "detail": "gpu_utils", "documentation": {}}, {"label": "generate_dual_language_srt", "kind": 2, "importPath": "quick_fix", "description": "quick_fix", "peekOfCode": "def generate_dual_language_srt(segments):\n    srt_content = []\n    print(f\"\\n[DEBUG generate_dual_language_srt] 处理{len(segments)}个segments\")\n    for i, segment in enumerate(segments, 1):\n        # 安全获取所有字段\n        start_time = format_timestamp(segment.get('start', 0))\n        end_time = format_timestamp(segment.get('end', 0))\n        chinese_text = str(segment.get('text', '')).strip()\n        # 更安全地获取翻译文本\n        translated = segment.get('translated', '')", "detail": "quick_fix", "documentation": {}}, {"label": "translated_segments", "kind": 5, "importPath": "quick_fix", "description": "quick_fix", "peekOfCode": "translated_segments = copy.deepcopy(segments)\n# 2. 改进解析逻辑（替换315-328行）\nfor line in lines:\n    line = line.strip()\n    if line:\n        # 尝试多种解析方式\n        import re\n        # 支持 [1] xxx, 1. xxx, 1) xxx, 1: xxx 等格式\n        patterns = [\n            r'^\\[(\\d+)\\]\\s*(.+)$',     # [1] translation", "detail": "quick_fix", "documentation": {}}, {"label": "check_requirements", "kind": 2, "importPath": "run_ai", "description": "run_ai", "peekOfCode": "def check_requirements():\n    \"\"\"检查必要的依赖\"\"\"\n    required_packages = {\n        'flask': 'Flask',\n        'whisper': 'openai-whisper',\n        'requests': 'requests'\n    }\n    missing = []\n    for module, package in required_packages.items():\n        try:", "detail": "run_ai", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "run_ai", "description": "run_ai", "peekOfCode": "def main():\n    print(\"=\"*60)\n    print(\"音频转文字AI工具 - 启动器\")\n    print(\"=\"*60)\n    print(\"\\n功能特点：\")\n    print(\"  ✓ 音频转文字（使用OpenAI Whisper）\")\n    print(\"  ✓ AI文本处理（使用DeepSeek-V3）\")\n    print(\"  ✓ 自定义提示语支持\")\n    print(\"  ✓ 多种AI工具：总结、大纲、关键词、润色、翻译\")\n    print(\"  ✓ 双语字幕生成\")", "detail": "run_ai", "documentation": {}}, {"label": "check_dependencies", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def check_dependencies():\n    \"\"\"检查必要的依赖是否已安装\"\"\"\n    try:\n        import flask\n        import whisper\n        import torch\n        return True\n    except ImportError as e:\n        print(f\"缺少依赖：{e}\")\n        print(\"请运行：pip install -r requirements.txt\")", "detail": "start", "documentation": {}}, {"label": "ensure_model_exists", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def ensure_model_exists(model_name):\n    \"\"\"确保模型文件存在\"\"\"\n    model_path = get_model_path(model_name)\n    if not os.path.exists(model_path):\n        print(f\"模型 {model_name} 不存在，正在下载...\")\n        return download_model(model_name)\n    return True\ndef main():\n    print(\"=== 音频转文字应用启动器 ===\\n\")\n    # 检查依赖", "detail": "start", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def main():\n    print(\"=== 音频转文字应用启动器 ===\\n\")\n    # 检查依赖\n    if not check_dependencies():\n        sys.exit(1)\n    # 显示已下载的模型\n    print(\"已下载的模型：\")\n    downloaded = list_downloaded_models()\n    if not downloaded:\n        print(\"  （无）\")", "detail": "start", "documentation": {}}, {"label": "test_audio_file", "kind": 2, "importPath": "test_audio_content", "description": "test_audio_content", "peekOfCode": "def test_audio_file():\n    audio_file = \"test_audio.wav\"\n    # Check WAV file properties\n    try:\n        with wave.open(audio_file, 'rb') as wav:\n            print(f\"Audio file: {audio_file}\")\n            print(f\"Channels: {wav.getnchannels()}\")\n            print(f\"Sample width: {wav.getsampwidth()} bytes\")\n            print(f\"Framerate: {wav.getframerate()} Hz\")\n            print(f\"Frames: {wav.getnframes()}\")", "detail": "test_audio_content", "documentation": {}}, {"label": "gpu_info", "kind": 5, "importPath": "test_gpu_app", "description": "test_gpu_app", "peekOfCode": "gpu_info = print_device_info()\n# 2. 测试获取最优设备\nprint(\"\\n2. 测试设备选择:\")\ndevice = get_optimal_device()\nprint(f\"选择的设备: {device}\")\n# 3. 测试Whisper模型加载\nprint(\"\\n3. 测试Whisper模型加载:\")\ntry:\n    import whisper\n    print(\"正在加载Whisper模型...\")", "detail": "test_gpu_app", "documentation": {}}, {"label": "device", "kind": 5, "importPath": "test_gpu_app", "description": "test_gpu_app", "peekOfCode": "device = get_optimal_device()\nprint(f\"选择的设备: {device}\")\n# 3. 测试Whisper模型加载\nprint(\"\\n3. 测试Whisper模型加载:\")\ntry:\n    import whisper\n    print(\"正在加载Whisper模型...\")\n    # 尝试加载小模型进行测试\n    model = whisper.load_model(\"base\")\n    print(\"✓ 模型加载成功\")", "detail": "test_gpu_app", "documentation": {}}, {"label": "test_audio", "kind": 5, "importPath": "test_gpu_app", "description": "test_gpu_app", "peekOfCode": "test_audio = \"test_audio.wav\"\nif not os.path.exists(test_audio):\n    print(\"创建一个静音测试文件...\")\n    import numpy as np\n    import wave\n    # 创建1秒的静音音频\n    sample_rate = 16000\n    duration = 1  # 秒\n    samples = np.zeros(int(sample_rate * duration), dtype=np.int16)\n    with wave.open(test_audio, 'w') as wav_file:", "detail": "test_gpu_app", "documentation": {}}, {"label": "test_generate_dual_subtitle", "kind": 2, "importPath": "test_translation_debug", "description": "test_translation_debug", "peekOfCode": "def test_generate_dual_subtitle():\n    \"\"\"测试双语字幕生成接口\"\"\"\n    url = \"http://localhost:5000/generate_dual_subtitle\"\n    payload = {\n        \"segments\": test_segments,\n        \"target_lang\": \"en\"\n    }\n    print(\"发送请求到:\", url)\n    print(\"请求数据:\", json.dumps(payload, ensure_ascii=False, indent=2))\n    try:", "detail": "test_translation_debug", "documentation": {}}, {"label": "test_translate_api", "kind": 2, "importPath": "test_translation_debug", "description": "test_translation_debug", "peekOfCode": "def test_translate_api():\n    \"\"\"测试单独的翻译接口\"\"\"\n    url = \"http://localhost:5000/translate\"\n    test_text = \"你好，欢迎使用语音转文字工具\"\n    payload = {\n        \"text\": test_text,\n        \"target\": \"en\"\n    }\n    print(\"\\n测试独立翻译接口:\")\n    print(\"翻译文本:\", test_text)", "detail": "test_translation_debug", "documentation": {}}, {"label": "test_segments", "kind": 5, "importPath": "test_translation_debug", "description": "test_translation_debug", "peekOfCode": "test_segments = [\n    {\n        \"start\": 0.0,\n        \"end\": 2.5,\n        \"text\": \"你好，欢迎使用语音转文字工具\"\n    },\n    {\n        \"start\": 2.5,\n        \"end\": 5.0,\n        \"text\": \"这是一个测试段落\"", "detail": "test_translation_debug", "documentation": {}}, {"label": "test_upload", "kind": 2, "importPath": "test_upload", "description": "test_upload", "peekOfCode": "def test_upload():\n    \"\"\"Test file upload to the server\"\"\"\n    url = \"http://localhost:5000/upload\"\n    # Create a test audio file if it doesn't exist\n    test_file = \"test_audio.wav\"\n    if not os.path.exists(test_file):\n        print(f\"Error: Test file '{test_file}' not found\")\n        print(\"Please provide a test audio file (wav, mp3, mp4, m4a, wma, or aac)\")\n        return\n    print(f\"Testing upload with file: {test_file}\")", "detail": "test_upload", "documentation": {}}, {"label": "test_whisper", "kind": 2, "importPath": "test_whisper_direct", "description": "test_whisper_direct", "peekOfCode": "def test_whisper():\n    print(\"=== Direct Whisper Test ===\\n\")\n    # Check environment\n    print(f\"PyTorch version: {torch.__version__}\")\n    print(f\"CUDA available: {torch.cuda.is_available()}\")\n    if torch.cuda.is_available():\n        print(f\"CUDA device: {torch.cuda.get_device_name(0)}\")\n    print()\n    # Test file\n    test_file = \"test_audio.wav\"", "detail": "test_whisper_direct", "documentation": {}}, {"label": "safe_load_whisper_model", "kind": 2, "importPath": "whisper_utils", "description": "whisper_utils", "peekOfCode": "def safe_load_whisper_model(model_name=\"small\"):\n    \"\"\"安全加载Whisper模型，自动处理设备和数据类型\"\"\"\n    print(f\"Loading Whisper model: {model_name}...\")\n    try:\n        # 首先尝试正常加载\n        device = get_optimal_device()\n        if device == 'cuda':\n            # GPU模式 - 使用float32以避免兼容性问题\n            model = whisper.load_model(model_name, device=\"cuda\")\n            model = model.float()  # 使用FP32而不是FP16", "detail": "whisper_utils", "documentation": {}}, {"label": "safe_transcribe", "kind": 2, "importPath": "whisper_utils", "description": "whisper_utils", "peekOfCode": "def safe_transcribe(model, audio_path, device='cpu'):\n    \"\"\"安全转录音频，自动处理设备兼容性问题\"\"\"\n    try:\n        # 统一使用float32以避免兼容性问题\n        if next(model.parameters()).dtype != torch.float32:\n            model = model.float()\n        # 根据设备选择合适的参数\n        if device == 'cuda' and torch.cuda.is_available():\n            # GPU模式 - 确保模型在GPU上\n            if next(model.parameters()).device.type != 'cuda':", "detail": "whisper_utils", "documentation": {}}]