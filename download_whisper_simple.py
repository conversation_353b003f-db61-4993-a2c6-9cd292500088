#!/usr/bin/env python
"""
简化版Whisper模型下载脚本
直接从Hugging Face镜像下载模型文件
"""
import os
import sys
import requests
from tqdm import tqdm
import hashlib

# Hugging Face镜像上的Whisper模型直接下载链接
# 这些是预训练模型的直接链接
WHISPER_MODELS = {
    "tiny": {
        "url": "https://hf-mirror.com/openai/whisper-tiny/resolve/main/pytorch_model.bin",
        "size_mb": 39,
        "filename": "tiny.pt"
    },
    "tiny.en": {
        "url": "https://hf-mirror.com/openai/whisper-tiny.en/resolve/main/pytorch_model.bin", 
        "size_mb": 39,
        "filename": "tiny.en.pt"
    },
    "base": {
        "url": "https://hf-mirror.com/openai/whisper-base/resolve/main/pytorch_model.bin",
        "size_mb": 74,
        "filename": "base.pt"
    },
    "base.en": {
        "url": "https://hf-mirror.com/openai/whisper-base.en/resolve/main/pytorch_model.bin",
        "size_mb": 74,
        "filename": "base.en.pt"
    },
    "small": {
        "url": "https://hf-mirror.com/openai/whisper-small/resolve/main/pytorch_model.bin",
        "size_mb": 244,
        "filename": "small.pt"
    },
    "small.en": {
        "url": "https://hf-mirror.com/openai/whisper-small.en/resolve/main/pytorch_model.bin",
        "size_mb": 244,
        "filename": "small.en.pt"
    },
    "medium": {
        "url": "https://hf-mirror.com/openai/whisper-medium/resolve/main/pytorch_model.bin",
        "size_mb": 769,
        "filename": "medium.pt"
    },
    "medium.en": {
        "url": "https://hf-mirror.com/openai/whisper-medium.en/resolve/main/pytorch_model.bin",
        "size_mb": 769,
        "filename": "medium.en.pt"
    },
    "large-v1": {
        "url": "https://hf-mirror.com/openai/whisper-large/resolve/main/pytorch_model.bin",
        "size_mb": 1550,
        "filename": "large-v1.pt"
    },
    "large-v2": {
        "url": "https://hf-mirror.com/openai/whisper-large-v2/resolve/main/pytorch_model.bin",
        "size_mb": 1550,
        "filename": "large-v2.pt"
    },
    "large": {
        "url": "https://hf-mirror.com/openai/whisper-large-v2/resolve/main/pytorch_model.bin",
        "size_mb": 1550,
        "filename": "large-v2.pt"
    }
}

def download_with_resume(url, dest_path, expected_size_mb=None):
    """支持断点续传的下载函数"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 获取已下载的大小
    resume_pos = 0
    mode = 'wb'
    if os.path.exists(dest_path):
        resume_pos = os.path.getsize(dest_path)
        # 检查文件是否已完整
        if expected_size_mb and resume_pos >= expected_size_mb * 1024 * 1024 * 0.95:  # 95%以上认为完整
            print(f"文件已存在且完整：{dest_path}")
            return True
        headers['Range'] = f'bytes={resume_pos}-'
        mode = 'ab'
    
    try:
        print(f"开始下载：{url}")
        response = requests.get(url, stream=True, headers=headers, timeout=30)
        
        # 处理响应
        if response.status_code == 416:  # 范围不可满足，文件已完整
            print("文件已完整下载")
            return True
        elif response.status_code not in [200, 206]:
            print(f"下载失败，HTTP状态码：{response.status_code}")
            return False
        
        # 获取文件总大小
        if response.status_code == 206:
            content_range = response.headers.get('content-range', '')
            if content_range:
                total_size = int(content_range.split('/')[-1])
            else:
                total_size = int(response.headers.get('content-length', 0)) + resume_pos
            print(f"继续下载，已下载：{resume_pos/1024/1024:.1f}MB")
        else:
            total_size = int(response.headers.get('content-length', 0))
            if resume_pos > 0:
                print("服务器不支持断点续传，重新开始下载")
                resume_pos = 0
                mode = 'wb'
        
        # 下载文件
        with open(dest_path, mode) as f:
            with tqdm(total=total_size, initial=resume_pos, unit='B', 
                     unit_scale=True, desc=os.path.basename(dest_path)) as pbar:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
        
        print(f"下载完成：{dest_path}")
        return True
        
    except requests.exceptions.Timeout:
        print("下载超时，请重新运行命令以继续下载")
        return False
    except Exception as e:
        print(f"下载出错：{e}")
        return False

def download_model(model_name):
    """下载指定的Whisper模型"""
    if model_name not in WHISPER_MODELS:
        print(f"错误：不支持的模型 '{model_name}'")
        print(f"支持的模型：{', '.join(WHISPER_MODELS.keys())}")
        return False
    
    model_info = WHISPER_MODELS[model_name]
    
    # 创建模型目录
    model_dir = os.path.join("models", "whisper")
    os.makedirs(model_dir, exist_ok=True)
    
    # 目标文件路径
    dest_path = os.path.join(model_dir, model_info["filename"])
    
    print(f"\n准备下载 Whisper {model_name} 模型")
    print(f"预计大小：{model_info['size_mb']} MB")
    print(f"保存位置：{dest_path}")
    print(f"下载源：Hugging Face 镜像站")
    print("-" * 50)
    
    # 下载模型
    success = download_with_resume(
        model_info["url"], 
        dest_path,
        model_info["size_mb"]
    )
    
    if success:
        actual_size = os.path.getsize(dest_path) / (1024 * 1024)
        print(f"\n✓ 下载成功！")
        print(f"文件大小：{actual_size:.1f} MB")
        print(f"模型路径：{dest_path}")
        return True
    else:
        print(f"\n✗ 下载失败")
        print("提示：如果下载中断，重新运行相同命令可以继续下载")
        return False

def list_models():
    """列出所有可用和已下载的模型"""
    model_dir = os.path.join("models", "whisper")
    
    print("\n=== 可用的Whisper模型 ===")
    print("\n多语言模型：")
    for name, info in WHISPER_MODELS.items():
        if not name.endswith('.en') and name not in ['large-v1', 'large-v2']:
            print(f"  {name:8} - {info['size_mb']:4}MB - {info['filename']}")
    
    print("\n仅英语模型（速度更快）：")
    for name, info in WHISPER_MODELS.items():
        if name.endswith('.en'):
            print(f"  {name:8} - {info['size_mb']:4}MB - {info['filename']}")
    
    print("\n大型模型版本：")
    print(f"  {'large':8} - {WHISPER_MODELS['large']['size_mb']:4}MB - 最新版本 (v2)")
    print(f"  {'large-v1':8} - {WHISPER_MODELS['large-v1']['size_mb']:4}MB - 旧版本")
    print(f"  {'large-v2':8} - {WHISPER_MODELS['large-v2']['size_mb']:4}MB - 最新版本")
    
    print("\n=== 已下载的模型 ===")
    if os.path.exists(model_dir):
        downloaded = []
        for filename in os.listdir(model_dir):
            if filename.endswith('.pt'):
                filepath = os.path.join(model_dir, filename)
                size_mb = os.path.getsize(filepath) / (1024 * 1024)
                downloaded.append((filename, size_mb))
        
        if downloaded:
            for filename, size_mb in downloaded:
                print(f"  ✓ {filename:12} - {size_mb:.1f}MB")
        else:
            print("  （无）")
    else:
        print("  （无）")

def main():
    if len(sys.argv) < 2:
        print("Whisper模型下载工具（使用Hugging Face镜像）")
        print("\n使用方法：")
        print(f"  python {os.path.basename(sys.argv[0])} <model>  # 下载指定模型")
        print(f"  python {os.path.basename(sys.argv[0])} list    # 查看所有模型")
        print("\n示例：")
        print(f"  python {os.path.basename(sys.argv[0])} base    # 下载base模型（推荐）")
        print(f"  python {os.path.basename(sys.argv[0])} tiny    # 下载最小的模型")
        print(f"  python {os.path.basename(sys.argv[0])} large   # 下载最大的模型")
        print("\n提示：")
        print("  - 支持断点续传，下载中断后重新运行即可继续")
        print("  - 英语专用模型（.en后缀）速度更快")
        print("  - 使用 'list' 命令查看所有可用模型")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "list":
        list_models()
    elif command in WHISPER_MODELS:
        success = download_model(command)
        sys.exit(0 if success else 1)
    else:
        print(f"错误：未知的模型或命令 '{command}'")
        print("使用 'list' 查看所有可用模型")
        sys.exit(1)

if __name__ == "__main__":
    main()