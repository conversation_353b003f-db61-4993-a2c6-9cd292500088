# 音频转文字AI工具

一个基于OpenAI Whisper的音频转文字应用，集成了AI文本处理功能，支持多种音频格式转换、AI智能处理和双语字幕生成。

## ✨ 功能特点

### 🎯 核心功能
- **音频转文字**：支持 wav、mp3、mp4、m4a、wma、aac 等多种格式
- **输出格式**：纯文本（.txt）或字幕文件（.srt）
- **本地部署**：模型完全在本地运行，保护隐私
- **进度显示**：实时显示转换进度

### 🤖 AI增强功能
- **智能总结**：生成文本的简洁摘要
- **大纲提取**：提取内容的主要观点和结构
- **关键词提取**：识别文本中的重要关键词
- **文本润色**：改进文本的表达和流畅度
- **多语言翻译**：支持中英日韩等多语言翻译
- **自定义处理**：支持用户自定义AI提示语

### 🌐 双语字幕
- 自动生成中英文双语字幕
- 支持多种目标语言（英、日、韩、西、法）
- SRT格式输出，兼容主流播放器

## 🚀 快速开始

### 系统要求
- Python 3.8+
- Windows/Linux/macOS
- 4GB+ RAM（使用small模型建议8GB+）
- 可选：NVIDIA GPU（支持CUDA）以加速处理

### 安装步骤

1. **克隆或下载项目**
```bash
cd D:\code_hub_xia\voice2txt
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置Whisper模型**

项目已包含 `whisper-small` 模型在 `models/whisper-small` 目录中。

如需下载其他模型：
```bash
python download_whisper_simple.py base    # 下载base模型
python download_whisper_simple.py list    # 查看可用模型
```

### 运行应用

```bash
python run_ai.py
```

访问 http://localhost:5000 使用应用。

## 📖 使用指南

### 基本使用流程
1. 打开浏览器访问 http://localhost:5000
2. 点击"选择音频文件"上传音频
3. 选择输出格式（纯文本或SRT字幕）
4. 点击"开始转换"等待处理完成
5. 在结果页面查看和下载转换结果

### AI工具使用
1. 完成音频转文字后，切换到"AI工具"标签
2. 选择所需的AI工具类型：
   - 总结摘要
   - 提取大纲
   - 关键词提取
   - 文本润色
   - 翻译
   - 自定义处理
3. 如选择"自定义处理"，输入您的提示语
4. 点击"开始处理"获取AI处理结果

### 双语字幕生成
1. 转换完成后切换到"双语字幕"标签
2. 选择目标语言
3. 点击"生成双语字幕"
4. 等待翻译完成后下载字幕文件

## 🛠️ 配置说明

### Whisper模型配置
Whisper模型在 `app_with_ai.py` 中默认使用 `small` 模型。可通过以下方式下载其他模型：
```bash
python download_whisper_simple.py base    # 下载base模型
python download_whisper_simple.py medium  # 下载medium模型
```

### AI API配置
编辑 `ai_config.py` 文件配置AI服务：
```python
AI_CONFIG = {
    "base_url": "https://api.siliconflow.cn/v1/chat/completions",
    "api_key": "your-api-key",
    "model": "deepseek-ai/DeepSeek-V3"
}
```

## 📁 项目结构

```
voice2txt/
├── run_ai.py              # 主启动器
├── app_with_ai.py         # 主应用（Flask服务器）
├── ai_config.py           # AI配置文件
├── gpu_utils.py           # GPU检测和管理工具
├── whisper_utils.py       # Whisper模型辅助工具
├── download_whisper_simple.py  # 模型下载工具
├── requirements.txt       # Python依赖
├── models/                # 模型文件目录
│   ├── whisper-small/    # 已包含的small模型
│   └── whisper/          # 其他下载的模型
├── templates/            # HTML模板
│   └── index_ai.html     # Web界面
├── static/               # 静态资源
│   ├── css/             # 样式文件
│   └── js/              # JavaScript文件
├── uploads/              # 临时上传目录
└── outputs/              # 输出文件目录
```

## ⚡ 性能优化

### GPU加速
应用会自动检测并使用GPU进行加速：
- 自动检测可用的NVIDIA GPU
- 启用FP16半精度加速（仅GPU模式）
- GPU失败时自动回退到CPU处理
- 处理完成后自动清理GPU内存

启动应用时会显示设备信息：
```
=== 设备信息 ===
GPU状态: 可用
GPU型号: NVIDIA GeForce RTX 3060
GPU数量: 1
CUDA版本: 11.8
================
```

### 模型选择建议
| 模型 | 大小 | 速度 | 准确度 | 适用场景 |
|------|------|------|--------|----------|
| tiny | 39MB | ⭐⭐⭐⭐⭐ | ⭐⭐ | 快速测试 |
| base | 74MB | ⭐⭐⭐⭐ | ⭐⭐⭐ | 日常使用 |
| small | 244MB | ⭐⭐⭐ | ⭐⭐⭐⭐ | 推荐使用 |
| medium | 769MB | ⭐⭐ | ⭐⭐⭐⭐ | 高质量需求 |
| large | 1.5GB | ⭐ | ⭐⭐⭐⭐⭐ | 最高精度 |

### 处理建议
- 长音频建议分段处理
- GPU加速提升：
  - tiny/base模型：2-3倍速度提升
  - small/medium模型：3-5倍速度提升
  - large模型：5-10倍速度提升
- 清晰的音频质量能提高识别准确度
- GPU内存要求：
  - tiny/base: 1-2GB
  - small: 2-4GB
  - medium: 4-6GB
  - large: 6-10GB

## 🔒 隐私说明
- 所有音频处理均在本地完成
- 音频文件处理后自动删除
- AI功能需要网络连接（可选）

## 📝 自定义提示语示例

- **会议纪要**："请将这段对话整理成会议纪要，包含时间、参与人、主要议题和行动项"
- **教学内容**："请将这段讲课内容整理成知识点大纲，标注重点和难点"
- **采访整理**："请将这段采访内容整理成问答形式，突出关键信息"
- **内容分析**："请分析这段内容的主题、观点和论据结构"

## 🐛 常见问题

**Q: 首次运行提示下载模型？**
A: 项目已包含small模型，如果提示下载，可能是模型文件不完整。运行 `python download_whisper_simple.py small` 重新下载。

**Q: 转换速度很慢？**
A: 
1. 检查是否使用了GPU（启动时会显示设备信息）
2. 如果没有GPU，可以使用更小的模型（如base或tiny）
3. 确保安装了正确的PyTorch CUDA版本：`pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118`

**Q: AI功能无法使用？**
A: 检查 `ai_config.py` 中的API配置是否正确，确保网络连接正常。

**Q: 支持什么音频格式？**
A: 支持 wav、mp3、mp4、m4a、wma、aac 等常见格式。

## 📄 许可证

本项目基于开源组件构建：
- OpenAI Whisper - MIT License
- Flask - BSD License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**注意**：使用AI功能需要配置API密钥。本地音频转文字功能无需网络连接。