* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f7;
    color: #1d1d1f;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

h1 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 40px;
    color: #1d1d1f;
}

h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #1d1d1f;
}

h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #1d1d1f;
}

h4 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #1d1d1f;
}

.upload-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.file-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-label {
    display: inline-block;
    padding: 12px 24px;
    background-color: #007AFF;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.file-label:hover {
    background-color: #0051D5;
}

.file-name {
    margin-left: 15px;
    color: #86868b;
}

.format-options {
    margin: 20px 0;
}

.format-options label {
    margin-right: 20px;
    cursor: pointer;
}

.format-options input[type="radio"] {
    margin-right: 5px;
}

.model-options {
    margin: 20px 0;
}

.model-options label {
    display: inline-block;
    margin-right: 10px;
    font-weight: 500;
}

.model-select {
    padding: 8px 15px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 16px;
    background-color: white;
    cursor: pointer;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 10px;
}

.btn-primary {
    background-color: #007AFF;
    color: white;
}

.btn-primary:hover {
    background-color: #0051D5;
}

.btn-secondary {
    background-color: #86868b;
    color: white;
}

.btn-secondary:hover {
    background-color: #666;
}

.progress {
    margin-top: 20px;
    background-color: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    height: 40px;
}

.progress-bar {
    background: linear-gradient(90deg, #007AFF 0%, #0051D5 100%);
    height: 100%;
    width: 0;
    transition: width 0.5s ease;
    position: absolute;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 122, 255, 0.5);
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.progress-info {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    z-index: 1;
}

.progress-text {
    font-weight: 500;
    color: #333;
}

.progress-percentage {
    font-weight: 600;
    color: #007AFF;
}

.results-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.result-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e5e5e7;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #86868b;
    transition: all 0.3s;
    border-bottom: 2px solid transparent;
}

.tab-btn:hover {
    color: #1d1d1f;
}

.tab-btn.active {
    color: #007AFF;
    border-bottom-color: #007AFF;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.result-textarea {
    width: 100%;
    min-height: 300px;
    padding: 15px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-family: 'SF Mono', Monaco, monospace;
    font-size: 14px;
    resize: vertical;
    margin-bottom: 20px;
}

.ai-tools-section {
    margin-bottom: 30px;
}

.tool-selector {
    margin-bottom: 20px;
}

.tool-select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 16px;
    background-color: white;
    cursor: pointer;
}

.custom-prompt-section {
    margin-bottom: 20px;
}

.prompt-textarea {
    width: 100%;
    min-height: 100px;
    padding: 15px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
}

.preset-prompts {
    margin-bottom: 20px;
}

.prompt-templates {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.prompt-template {
    padding: 8px 16px;
    background-color: #f0f0f0;
    border: 1px solid #e5e5e7;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.prompt-template:hover {
    background-color: #007AFF;
    color: white;
    border-color: #007AFF;
}

.ai-result {
    margin-top: 30px;
}

.result-actions {
    display: flex;
    gap: 10px;
}

.language-options {
    margin-bottom: 20px;
}

.language-select {
    padding: 8px 15px;
    border: 1px solid #e5e5e7;
    border-radius: 8px;
    font-size: 16px;
    margin-left: 10px;
}

.subtitle-section {
    margin-bottom: 30px;
}

.tool-result {
    margin-top: 20px;
}

.hidden {
    display: none;
}

@media (max-width: 768px) {
    .container {
        padding: 20px 15px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .upload-section,
    .results-section {
        padding: 20px;
    }
    
    .result-tabs {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        font-size: 14px;
        padding: 8px 15px;
    }
    
    .prompt-templates {
        flex-direction: column;
    }
    
    .prompt-template {
        width: 100%;
        text-align: center;
    }
}