let currentTranscription = null;
let currentOutputFile = null;

document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const audioFile = document.getElementById('audioFile');
    const fileName = document.getElementById('fileName');
    const uploadBtn = document.getElementById('uploadBtn');
    const progress = document.getElementById('progress');
    const results = document.getElementById('results');
    
    // 文件选择
    audioFile.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileName.textContent = file.name;
        } else {
            fileName.textContent = '未选择文件';
        }
    });
    
    // 上传表单提交
    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const file = audioFile.files[0];
        if (!file) {
            alert('请选择一个音频文件');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('format', document.querySelector('input[name="format"]:checked').value);
        
        // 显示进度
        progress.classList.remove('hidden');
        uploadBtn.disabled = true;
        results.classList.add('hidden');
        
        // 重置进度条
        updateProgress(0, '正在上传文件...');
        
        try {
            // 显示初始进度
            updateProgress(5, '正在准备上传...');
            
            // 计算文件大小（MB）
            const fileSizeMB = file.size / (1024 * 1024);
            const estimatedTime = Math.max(5, fileSizeMB * 2); // 估算处理时间（秒）
            
            // 模拟上传进度
            setTimeout(() => updateProgress(10, '正在上传文件...'), 100);
            setTimeout(() => updateProgress(20, `正在上传文件... (${fileSizeMB.toFixed(1)}MB)`), 500);
            
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                // 更新进度 - 文件已上传
                updateProgress(30, '文件上传完成，正在加载音频...');
                
                // 根据文件大小调整进度更新
                const stages = [
                    { progress: 40, text: '正在预处理音频...' },
                    { progress: 50, text: '正在识别语音...' },
                    { progress: 60, text: '正在转换为文字...' },
                    { progress: 70, text: '正在处理语音片段...' },
                    { progress: 80, text: '正在优化识别结果...' },
                    { progress: 90, text: '正在生成输出文件...' }
                ];
                
                let stageIndex = 0;
                const progressInterval = setInterval(() => {
                    if (stageIndex < stages.length) {
                        const stage = stages[stageIndex];
                        updateProgress(stage.progress, stage.text);
                        stageIndex++;
                    }
                }, Math.min(2000, (estimatedTime * 1000) / stages.length));
                
                const data = await response.json();
                
                clearInterval(progressInterval);
                
                if (data.success) {
                    updateProgress(100, '转换完成！');
                    
                    setTimeout(() => {
                        currentTranscription = data;
                        currentOutputFile = data.output_file;
                        displayResults(data);
                        progress.classList.add('hidden');
                        uploadBtn.disabled = false;
                    }, 500);
                } else {
                    alert('转换失败: ' + data.error);
                    progress.classList.add('hidden');
                    uploadBtn.disabled = false;
                }
            } else {
                throw new Error('上传失败');
            }
        } catch (error) {
            alert('上传失败: ' + error.message);
            progress.classList.add('hidden');
            uploadBtn.disabled = false;
        }
    });
    
    // 更新进度条
    function updateProgress(percentage, text) {
        const progressBar = document.querySelector('.progress-bar');
        const progressText = document.querySelector('.progress-text');
        const progressPercentage = document.querySelector('.progress-percentage');
        
        progressBar.style.width = percentage + '%';
        progressText.textContent = text;
        progressPercentage.textContent = percentage + '%';
    }
    
    // Tab切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 切换tab按钮状态
            tabBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 切换tab内容
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === targetTab + 'Tab') {
                    content.classList.add('active');
                }
            });
        });
    });
    
    // 下载文本
    document.getElementById('downloadText').addEventListener('click', function() {
        if (currentOutputFile) {
            window.location.href = '/download/' + currentOutputFile;
        }
    });
    
    // AI总结
    document.getElementById('summarizeBtn').addEventListener('click', async function() {
        if (!currentTranscription) return;
        
        this.disabled = true;
        
        try {
            const response = await fetch('/summarize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: currentTranscription.text
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('summaryText').value = data.summary;
                document.getElementById('summaryResult').classList.remove('hidden');
            } else {
                alert('总结生成失败: ' + data.error);
            }
        } catch (error) {
            alert('请求失败: ' + error.message);
        } finally {
            this.disabled = false;
        }
    });
    
    // 生成双语字幕
    document.getElementById('generateDualSubtitle').addEventListener('click', async function() {
        if (!currentTranscription || !currentTranscription.segments) return;
        
        this.disabled = true;
        
        try {
            // 首先翻译每个片段
            const translatedSegments = [];
            for (const segment of currentTranscription.segments) {
                const translateResponse = await fetch('/translate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: segment.text,
                        target: 'en'
                    })
                });
                
                const translateData = await translateResponse.json();
                translatedSegments.push({
                    ...segment,
                    translated: translateData.translated || segment.text
                });
            }
            
            // 生成双语字幕
            const response = await fetch('/generate_dual_subtitle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    segments: translatedSegments
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('dualSubtitleText').value = data.content;
                document.getElementById('dualSubtitleResult').classList.remove('hidden');
                
                // 设置下载按钮
                document.getElementById('downloadDualSubtitle').onclick = function() {
                    window.location.href = '/download/' + data.filename;
                };
            } else {
                alert('双语字幕生成失败: ' + data.error);
            }
        } catch (error) {
            alert('请求失败: ' + error.message);
        } finally {
            this.disabled = false;
        }
    });
});

function displayResults(data) {
    document.getElementById('transcriptionText').value = data.text;
    document.getElementById('results').classList.remove('hidden');
    
    // 重置其他标签页的内容
    document.getElementById('summaryResult').classList.add('hidden');
    document.getElementById('dualSubtitleResult').classList.add('hidden');
}