let currentTranscription = null;
let currentOutputFile = null;

document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const audioFile = document.getElementById('audioFile');
    const fileName = document.getElementById('fileName');
    const uploadBtn = document.getElementById('uploadBtn');
    const progress = document.getElementById('progress');
    const results = document.getElementById('results');
    
    // AI工具相关元素
    const aiToolSelect = document.getElementById('aiToolSelect');
    const customPromptSection = document.getElementById('customPromptSection');
    const presetPrompts = document.getElementById('presetPrompts');
    const customPrompt = document.getElementById('customPrompt');
    const processBtn = document.getElementById('processBtn');
    const aiResult = document.getElementById('aiResult');
    
    // 文件选择
    audioFile.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileName.textContent = file.name;
        } else {
            fileName.textContent = '未选择文件';
        }
    });
    
    // AI工具选择
    aiToolSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customPromptSection.classList.remove('hidden');
            presetPrompts.classList.remove('hidden');
        } else {
            customPromptSection.classList.add('hidden');
            presetPrompts.classList.add('hidden');
        }
    });
    
    // 预设提示语模板点击
    document.querySelectorAll('.prompt-template').forEach(btn => {
        btn.addEventListener('click', function() {
            customPrompt.value = this.getAttribute('data-prompt');
        });
    });
    
    // 上传表单提交
    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const file = audioFile.files[0];
        if (!file) {
            alert('请选择一个音频文件');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('format', document.querySelector('input[name="format"]:checked').value);
        formData.append('model', document.getElementById('modelSelect').value);
        
        // 显示进度
        progress.classList.remove('hidden');
        uploadBtn.disabled = true;
        results.classList.add('hidden');
        
        // 重置进度条
        updateProgress(0, '准备中...', progress);
        
        try {
            // 显示初始进度
            updateProgress(5, '正在准备上传...', progress);
            
            // 计算文件大小（MB）
            const fileSizeMB = file.size / (1024 * 1024);
            
            // 创建XMLHttpRequest来跟踪上传进度
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 30); // 上传占30%
                    updateProgress(percentComplete, `正在上传文件... (${(e.loaded / (1024 * 1024)).toFixed(1)}/${fileSizeMB.toFixed(1)}MB)`, progress);
                }
            });
            
            // 创建Promise来处理请求
            const uploadPromise = new Promise((resolve, reject) => {
                xhr.onload = function() {
                    console.log('XHR响应:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText.substring(0, 200)
                    });
                    if (xhr.status === 200) {
                        try {
                            resolve(JSON.parse(xhr.responseText));
                        } catch (e) {
                            console.error('解析响应失败:', e);
                            reject(new Error('服务器响应格式错误'));
                        }
                    } else {
                        try {
                            const errorData = JSON.parse(xhr.responseText);
                            reject(new Error(errorData.error || `HTTP error! status: ${xhr.status}`));
                        } catch {
                            reject(new Error(`HTTP error! status: ${xhr.status}`));
                        }
                    }
                };
                
                xhr.onerror = function() {
                    console.error('XHR错误:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        readyState: xhr.readyState,
                        responseText: xhr.responseText
                    });
                    reject(new Error(`网络错误: ${xhr.statusText || '无法连接到服务器'}`));
                };
                
                xhr.ontimeout = function() {
                    console.error('请求超时');
                    reject(new Error('请求超时，请检查文件大小或网络连接'));
                };
                
                xhr.open('POST', '/upload');
                xhr.timeout = 300000; // 设置5分钟超时
                console.log('开始上传文件:', {
                    filename: file.name,
                    size: file.size,
                    type: file.type,
                    url: window.location.origin + '/upload'
                });
                xhr.send(formData);
            });
            
            // 当上传完成后，显示语音识别进度
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    updateProgress(30, '文件上传完成，正在进行语音识别...', progress);
                    
                    // 语音识别进度阶段
                    const stages = [
                        { progress: 40, text: '正在加载Whisper模型...' },
                        { progress: 50, text: '正在进行语音识别...' },
                        { progress: 60, text: '正在识别语音内容...' },
                        { progress: 70, text: '正在处理语音片段...' },
                        { progress: 80, text: '正在优化识别结果...' },
                        { progress: 90, text: '正在生成文本文件...' }
                    ];
                    
                    let stageIndex = 0;
                    const progressInterval = setInterval(() => {
                        if (stageIndex < stages.length) {
                            const stage = stages[stageIndex];
                            updateProgress(stage.progress, stage.text, progress);
                            stageIndex++;
                        }
                    }, 1500);
                    
                    // 等待服务器响应后清除定时器
                    uploadPromise.then(() => {
                        clearInterval(progressInterval);
                    }).catch(() => {
                        clearInterval(progressInterval);
                    });
                }
            });
            
            // 等待上传和处理完成
            const data = await uploadPromise;
            
            if (data.success) {
                updateProgress(100, '转换完成！', progress);
                
                setTimeout(() => {
                    currentTranscription = data;
                    currentOutputFile = data.output_file;
                    displayResults(data);
                    progress.classList.add('hidden');
                    uploadBtn.disabled = false;
                }, 500);
            } else {
                alert('转换失败: ' + data.error);
                progress.classList.add('hidden');
                uploadBtn.disabled = false;
            }
        } catch (error) {
            console.error('上传错误详情:', error);
            alert('上传失败: ' + error.message);
            progress.classList.add('hidden');
            uploadBtn.disabled = false;
            
            // 提供更详细的错误信息
            if (error.message.includes('网络错误')) {
                console.log('可能的原因:');
                console.log('1. Flask服务器未启动');
                console.log('2. 端口5000被占用');
                console.log('3. 防火墙阻止了连接');
                console.log('4. 文件太大超过了服务器限制');
            }
        }
    });
    
    // AI处理按钮
    processBtn.addEventListener('click', async function() {
        if (!currentTranscription || !currentTranscription.text) {
            alert('请先进行音频转文字');
            return;
        }
        
        const toolType = aiToolSelect.value;
        const customPromptText = customPrompt.value.trim();
        
        if (toolType === 'custom' && !customPromptText) {
            alert('请输入自定义提示语');
            return;
        }
        
        this.disabled = true;
        aiResult.classList.add('hidden');
        
        try {
            const response = await fetch('/ai_process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: currentTranscription.text,
                    tool_type: toolType,
                    custom_prompt: customPromptText
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('aiResultTitle').textContent = data.tool_name + ' - 处理结果';
                // 将markdown转换为HTML并显示
                const htmlContent = convertMarkdownToHtml(data.result);
                const aiResultText = document.getElementById('aiResultText');
                // 将textarea改为div显示HTML内容
                aiResultText.innerHTML = htmlContent;
                aiResultText.style.whiteSpace = 'pre-wrap';
                aiResultText.style.fontFamily = 'inherit';
                aiResult.classList.remove('hidden');
            } else {
                alert('处理失败: ' + data.error);
            }
        } catch (error) {
            alert('请求失败: ' + error.message);
        } finally {
            this.disabled = false;
        }
    });
    
    // 复制结果
    document.getElementById('copyResult').addEventListener('click', function() {
        const resultText = document.getElementById('aiResultText');
        // 获取纯文本内容
        const textContent = resultText.innerText || resultText.textContent;
        
        // 创建临时textarea来复制
        const tempTextarea = document.createElement('textarea');
        tempTextarea.value = textContent;
        document.body.appendChild(tempTextarea);
        tempTextarea.select();
        document.execCommand('copy');
        document.body.removeChild(tempTextarea);
        
        // 显示复制成功提示
        const originalText = this.textContent;
        this.textContent = '已复制！';
        setTimeout(() => {
            this.textContent = originalText;
        }, 2000);
    });
    
    // 下载AI处理结果
    document.getElementById('downloadResult').addEventListener('click', function() {
        const resultDiv = document.getElementById('aiResultText');
        const resultText = resultDiv.innerText || resultDiv.textContent;
        const toolName = document.getElementById('aiResultTitle').textContent;
        const blob = new Blob([resultText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${toolName}_${new Date().getTime()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });
    
    // 更新进度条
    function updateProgress(percentage, text, progressElement) {
        const progressBar = progressElement.querySelector('.progress-bar');
        const progressText = progressElement.querySelector('.progress-text');
        const progressPercentage = progressElement.querySelector('.progress-percentage');
        
        progressBar.style.width = percentage + '%';
        progressText.textContent = text;
        progressPercentage.textContent = percentage + '%';
        
        if (percentage === 100) {
            progressBar.style.background = 'linear-gradient(90deg, #28a745 0%, #218838 100%)';
        }
    }
    
    // 简单的Markdown转HTML函数
    function convertMarkdownToHtml(markdown) {
        // 转换标题
        let html = markdown
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>');
        
        // 转换粗体
        html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
        
        // 转换斜体
        html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>');
        
        // 转换代码块
        html = html.replace(/```([^`]+)```/g, '<pre><code>$1</code></pre>');
        
        // 转换内联代码
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // 转换列表
        html = html.replace(/^\* (.+)$/gim, '<li>$1</li>');
        html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        
        // 转换有序列表
        html = html.replace(/^\d+\. (.+)$/gim, '<li>$1</li>');
        
        // 转换换行
        html = html.replace(/\n\n/g, '</p><p>');
        html = '<p>' + html + '</p>';
        
        // 清理空段落
        html = html.replace(/<p><\/p>/g, '');
        
        return html;
    }
    
    // Tab切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            tabBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === targetTab + 'Tab') {
                    content.classList.add('active');
                }
            });
        });
    });
    
    // 下载文本
    document.getElementById('downloadText').addEventListener('click', function() {
        if (currentOutputFile) {
            window.location.href = '/download/' + currentOutputFile;
        }
    });
    
    // 生成双语字幕
    document.getElementById('generateDualSubtitle').addEventListener('click', async function() {
        if (!currentTranscription || !currentTranscription.segments) {
            alert('请先进行音频转文字');
            return;
        }
        
        const targetLang = document.getElementById('targetLanguage').value;
        const subtitleProgress = document.getElementById('subtitleProgress');
        
        this.disabled = true;
        subtitleProgress.classList.remove('hidden');
        updateProgress(0, '正在准备翻译...', subtitleProgress);
        
        try {
            updateProgress(20, '正在批量翻译字幕...', subtitleProgress);
            
            // 直接发送所有segments进行批量翻译
            const response = await fetch('/generate_dual_subtitle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    segments: currentTranscription.segments,
                    target_lang: targetLang
                })
            });
            
            // 更新进度
            updateProgress(50, '正在处理翻译结果...', subtitleProgress);
            
            const data = await response.json();
            
            if (data.success) {
                updateProgress(100, '双语字幕生成完成！', subtitleProgress);
                
                document.getElementById('dualSubtitleText').value = data.content;
                document.getElementById('dualSubtitleResult').classList.remove('hidden');
                
                // 设置下载按钮
                document.getElementById('downloadDualSubtitle').onclick = function() {
                    window.location.href = '/download/' + data.filename;
                };
                
                setTimeout(() => {
                    subtitleProgress.classList.add('hidden');
                }, 2000);
            } else {
                alert('双语字幕生成失败: ' + data.error);
                subtitleProgress.classList.add('hidden');
            }
        } catch (error) {
            alert('请求失败: ' + error.message);
            subtitleProgress.classList.add('hidden');
        } finally {
            this.disabled = false;
        }
    });
});

function displayResults(data) {
    document.getElementById('transcriptionText').value = data.text;
    document.getElementById('results').classList.remove('hidden');
    
    // 显示使用的模型信息（如果有）
    if (data.model_used) {
        const modelInfo = document.createElement('div');
        modelInfo.className = 'model-info';
        modelInfo.style.cssText = 'margin-bottom: 10px; padding: 10px; background: #f0f0f0; border-radius: 5px; font-size: 14px;';
        modelInfo.textContent = `使用模型: ${data.model_used === 'small' ? 'Whisper Small' : 'Whisper Large-v3'} | 设备: ${data.device_used || 'CPU'}`;
        
        const textTab = document.getElementById('textTab');
        const existingInfo = textTab.querySelector('.model-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        textTab.insertBefore(modelInfo, textTab.firstChild);
    }
    
    // 重置其他标签页的内容
    document.getElementById('aiResult').classList.add('hidden');
    document.getElementById('dualSubtitleResult').classList.add('hidden');
}