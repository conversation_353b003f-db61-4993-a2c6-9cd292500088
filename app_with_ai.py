from flask import Flask, render_template, request, jsonify, send_file
import os
import whisper
from werkzeug.utils import secure_filename
from datetime import datetime
import requests
import json
import torch
from ai_config import AI_CONFIG, AI_PROMPTS, ERROR_MESSAGES
from gpu_utils import print_device_info, setup_whisper_device, get_optimal_device, clear_gpu_cache
from whisper_utils import safe_load_whisper_model, safe_transcribe

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'outputs'

# 创建必要的文件夹
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'wav', 'mp3', 'mp4', 'm4a', 'wma', 'aac'}

# 显示设备信息
gpu_info = print_device_info()

# 初始化模型存储
loaded_models = {}
current_model = None
current_model_name = None

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def call_ai_api(prompt, text, temperature=None):
    """调用AI API，带重试机制"""
    import time
    
    max_retries = AI_CONFIG.get('max_retries', 3)
    timeout = AI_CONFIG.get('timeout', 900)
    
    for attempt in range(max_retries):
        try:
            headers = {
                "Authorization": f"Bearer {AI_CONFIG['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": AI_CONFIG['model'],
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的文本处理助手。请根据用户的要求处理文本内容。"
                    },
                    {
                        "role": "user",
                        "content": prompt.format(text=text)
                    }
                ],
                "temperature": temperature or AI_CONFIG['default_temperature'],
                "max_tokens": AI_CONFIG['max_tokens']
            }
            
            response = requests.post(
                AI_CONFIG['base_url'],
                headers=headers,
                json=data,
                timeout=timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'content': result['choices'][0]['message']['content']
                }
            else:
                error_msg = f"API错误: {response.status_code}"
                if attempt < max_retries - 1:
                    print(f"[API] 第{attempt + 1}次尝试失败: {error_msg}，等待后重试...")
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                print(f"[API] 第{attempt + 1}次尝试超时，等待后重试...")
                time.sleep(2 ** attempt)
                continue
            return {'success': False, 'error': '请求超时，请重试'}
            
        except (requests.exceptions.ProxyError, requests.exceptions.SSLError) as e:
            if attempt < max_retries - 1:
                print(f"[API] 第{attempt + 1}次网络错误: {type(e).__name__}，等待后重试...")
                time.sleep(2 ** attempt)
                continue
            return {'success': False, 'error': f'网络连接错误: {type(e).__name__}'}
            
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"[API] 第{attempt + 1}次其他错误: {str(e)}，等待后重试...")
                time.sleep(2 ** attempt)
                continue
            return {'success': False, 'error': str(e)}
    
    return {'success': False, 'error': f'重试{max_retries}次后仍然失败'}

@app.route('/')
def index():
    return render_template('index_ai.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    global current_model, current_model_name, loaded_models
    print(f"[Upload] Request received at {datetime.now()}")
    
    if 'file' not in request.files:
        print("[Upload] Error: No file part in request")
        return jsonify({'error': 'No file part'}), 400
    
    file = request.files['file']
    if file.filename == '':
        print("[Upload] Error: No selected file")
        return jsonify({'error': 'No selected file'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        
        print(f"[Upload] Filename: {filename}")
        print(f"[Upload] Filepath: {filepath}")
        print(f"[Upload] File size: {file.content_length} bytes")
        
        try:
            file.save(filepath)
            print(f"[Upload] File saved successfully")
        except Exception as e:
            print(f"[Upload] Error saving file: {e}")
            return jsonify({'error': f'Failed to save file: {str(e)}'}), 500
        
        # 获取输出格式和模型选择
        output_format = request.form.get('format', 'text')
        selected_model = request.form.get('model', 'small')
        
        # 验证模型选择
        if selected_model not in ['small', 'large-v3']:
            selected_model = 'small'
        
        # 如果需要切换模型
        if current_model_name != selected_model:
            print(f"[Upload] Switching model from {current_model_name} to {selected_model}")
            
            # 检查是否已经加载过该模型
            if selected_model in loaded_models:
                current_model, device = loaded_models[selected_model]
                current_model_name = selected_model
                print(f"[Upload] Using cached {selected_model} model")
            else:
                # 清理GPU缓存
                if current_model is not None and device == 'cuda':
                    clear_gpu_cache()
                
                # 加载新模型
                try:
                    current_model, device = safe_load_whisper_model(selected_model)
                    loaded_models[selected_model] = (current_model, device)
                    current_model_name = selected_model
                    print(f"[Upload] Successfully loaded {selected_model} model")
                except Exception as e:
                    print(f"[Upload] Failed to load {selected_model} model: {e}")
                    # 如果加载失败，尝试使用small模型
                    if selected_model != 'small':
                        try:
                            current_model, device = safe_load_whisper_model('small')
                            loaded_models['small'] = (current_model, device)
                            current_model_name = 'small'
                            print("[Upload] Falling back to small model")
                        except Exception as e2:
                            return jsonify({'error': f'Failed to load any model: {str(e2)}'}), 500
                    else:
                        return jsonify({'error': f'Failed to load model: {str(e)}'}), 500
        
        try:
            # 使用Whisper进行转录
            print(f"Transcribing {filename}...")
            print(f"Using device: {device}")
            
            # 使用安全转录方法
            result = safe_transcribe(current_model, filepath, device)
            
            # 清理GPU缓存
            if device == 'cuda':
                clear_gpu_cache()
            
            # 根据格式生成输出
            if output_format == 'srt':
                output = generate_srt(result)
                output_filename = f"{os.path.splitext(filename)[0]}.srt"
            else:
                output = result['text']
                output_filename = f"{os.path.splitext(filename)[0]}.txt"
            
            # 保存输出文件
            output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output)
            
            return jsonify({
                'success': True,
                'text': result['text'],
                'segments': result['segments'],
                'output_file': output_filename,
                'device_used': device,
                'model_used': current_model_name
            })
            
        except Exception as e:
            print(f"Transcription error: {e}")
            return jsonify({'error': f'Transcription failed: {str(e)}'}), 500
        finally:
            # 清理上传的文件
            if os.path.exists(filepath):
                os.remove(filepath)
    
    else:
        print(f"[Upload] Error: Invalid file type - {file.filename}")
        print(f"[Upload] Allowed extensions: {ALLOWED_EXTENSIONS}")
        return jsonify({'error': f'Invalid file type. Allowed types: {", ".join(ALLOWED_EXTENSIONS)}'}), 400

@app.route('/whisper_models', methods=['GET'])
def get_whisper_models():
    """获取可用的Whisper模型列表"""
    models = [
        {
            'id': 'small',
            'name': 'Whisper Small',
            'description': '较小的模型，速度快，准确度适中'
        },
        {
            'id': 'large-v3',
            'name': 'Whisper Large-v3',
            'description': '大型模型，速度较慢，准确度高'
        }
    ]
    return jsonify({
        'models': models,
        'current': current_model_name if current_model_name else 'small'
    })

@app.route('/ai_process', methods=['POST'])
def ai_process():
    """使用AI处理文本"""
    data = request.get_json()
    text = data.get('text', '')
    tool_type = data.get('tool_type', 'summarize')
    custom_prompt = data.get('custom_prompt', '')
    
    if not text:
        return jsonify({'error': ERROR_MESSAGES['empty_text']}), 400
    
    # 获取提示语
    if tool_type == 'custom':
        if not custom_prompt:
            return jsonify({'error': ERROR_MESSAGES['empty_prompt']}), 400
        prompt = AI_PROMPTS['custom']['default_prompt'].replace('{custom_prompt}', custom_prompt)
    else:
        prompt_config = AI_PROMPTS.get(tool_type, AI_PROMPTS['summarize'])
        prompt = prompt_config['default_prompt']
    
    # 调用AI API
    result = call_ai_api(prompt, text)
    
    if result['success']:
        return jsonify({
            'success': True,
            'result': result['content'],
            'tool_name': AI_PROMPTS.get(tool_type, {}).get('name', '自定义处理')
        })
    else:
        return jsonify({
            'success': False,
            'error': result['error']
        }), 500

@app.route('/ai_tools', methods=['GET'])
def get_ai_tools():
    """获取可用的AI工具列表"""
    tools = []
    for key, value in AI_PROMPTS.items():
        tools.append({
            'id': key,
            'name': value['name'],
            'description': value['description']
        })
    return jsonify({'tools': tools})

@app.route('/translate', methods=['POST'])
def translate_text():
    """翻译文本"""
    data = request.get_json()
    text = data.get('text', '')
    target_lang = data.get('target', 'en')
    
    if not text:
        return jsonify({'error': 'No text provided'}), 400
    
    # 构建翻译提示语
    lang_map = {
        'en': '英文',
        'ja': '日文',
        'ko': '韩文',
        'es': '西班牙语',
        'fr': '法语'
    }
    
    target_lang_name = lang_map.get(target_lang, '英文')
    prompt = f"请将以下中文内容准确翻译成{target_lang_name}，保持原意和语言风格：\n\n{{text}}"
    
    result = call_ai_api(prompt, text)
    
    if result['success']:
        return jsonify({
            'success': True,
            'translated': result['content']
        })
    else:
        return jsonify({'error': result['error']}), 500

@app.route('/generate_dual_subtitle', methods=['POST'])
def generate_dual_subtitle():
    """生成双语字幕"""
    data = request.get_json()
    segments = data.get('segments', [])
    target_lang = data.get('target_lang', 'en')
    
    print(f"\n[DEBUG] 接收到的segments数量: {len(segments)}")
    print(f"[DEBUG] 目标语言: {target_lang}")
    if segments:
        print(f"[DEBUG] 第一个segment内容: {segments[0]}")
    
    if not segments:
        return jsonify({'error': 'No segments provided'}), 400
    
    try:
        # 提取所有需要翻译的文本
        texts_to_translate = []
        valid_indices = []
        
        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            # Whisper有时会在中文前后加空格，需要额外清理
            text = text.strip(' \u3000\t\n\r')  # 清理全角和半角空格
            if text:
                texts_to_translate.append(text)
                valid_indices.append(i)
        
        # 使用深拷贝确保不会修改原始数据
        import copy
        translated_segments = copy.deepcopy(segments)
        
        print(f"\n[DEBUG] texts_to_translate: {texts_to_translate[:3]}...")  # 只显示前3个
        print(f"[DEBUG] valid_indices: {valid_indices[:10]}...")  # 只显示前10个
        
        if texts_to_translate:
            # 构建批量翻译的提示
            combined_text = "\n".join([f"[{i+1}] {text}" for i, text in enumerate(texts_to_translate)])
            
            print(f"\n[DEBUG] 要翻译的文本数量: {len(texts_to_translate)}")
            print(f"[DEBUG] 组合后的文本:\n{combined_text}")
            
            # 根据目标语言构建提示
            lang_map = {
                'zh': '中文',
                'en': '英文',
                'ja': '日文',
                'ko': '韩文',
                'es': '西班牙语',
                'fr': '法语'
            }
            
            target_lang_name = lang_map.get(target_lang, '英文')
            
            # 检测源语言（简单判断：如果大部分是英文字符，则认为是英文）
            is_english_source = False
            for text in texts_to_translate[:min(3, len(texts_to_translate))]:  # 检查前3个文本
                english_chars = sum(1 for c in text if c.isascii() and c.isalpha())
                total_chars = sum(1 for c in text if c.isalpha())
                print(f"[DEBUG] 检测文本: {text[:30]}...")
                print(f"[DEBUG] 英文字符数: {english_chars}, 总字符数: {total_chars}")
                if total_chars > 0 and english_chars / total_chars > 0.7:
                    is_english_source = True
                    break
            
            source_lang_name = '英文' if is_english_source else '中文'
            
            prompt = f"""请将以下编号的{source_lang_name}内容翻译成{target_lang_name}。保持每条翻译简洁准确。
请严格按照以下格式返回翻译结果，每行一个翻译，不要包含其他内容：
[1] 翻译内容1
[2] 翻译内容2
...

需要翻译的内容：
{{text}}"""
            
            print(f"[DEBUG] 源语言: {source_lang_name}")
            print(f"[DEBUG] 目标语言: {target_lang} -> {target_lang_name}")
            print(f"[DEBUG] 需要翻译的文本数量: {len(texts_to_translate)}")
            
            # 如果源语言和目标语言相同，直接使用原文
            if (is_english_source and target_lang == 'en') or (not is_english_source and target_lang == 'zh'):
                print("[DEBUG] 源语言和目标语言相同，跳过翻译")
                print(f"[DEBUG] is_english_source={is_english_source}, target_lang={target_lang}")
                for i, idx in enumerate(valid_indices):
                    translated_segments[idx] = {
                        **segments[idx],
                        'translated': segments[idx]['text']
                    }
            else:
                print(f"[DEBUG] 需要翻译: {source_lang_name} -> {target_lang_name}")
                
                # 如果文本太多，分批处理
                batch_size = 20  # 减小批次大小以提高成功率
                if len(texts_to_translate) > batch_size:
                    print(f"[DEBUG] 文本数量较多({len(texts_to_translate)}个)，将分批处理")
                    all_translations = {}
                    failed_batches = []  # 记录失败的批次
                    
                    for batch_start in range(0, len(texts_to_translate), batch_size):
                        batch_end = min(batch_start + batch_size, len(texts_to_translate))
                        batch_texts = texts_to_translate[batch_start:batch_end]
                        batch_combined = "\n".join([f"[{i+1}] {text}" for i, text in enumerate(batch_texts)])
                        
                        print(f"[DEBUG] 处理第 {batch_start//batch_size + 1} 批 ({batch_start+1}-{batch_end}/{len(texts_to_translate)})")
                        
                        # 调用AI进行批量翻译
                        result = call_ai_api(prompt, batch_combined, temperature=0.3)
                        
                        if result['success']:
                            # 解析这一批的翻译结果
                            lines = result['content'].strip().split('\n')
                            batch_translation_count = 0
                            for line in lines:
                                line = line.strip()
                                if line and line.startswith('[') and ']' in line:
                                    try:
                                        bracket_end = line.index(']')
                                        num_str = line[1:bracket_end]
                                        num = int(num_str) - 1 + batch_start  # 调整索引
                                        translation = line[bracket_end + 1:].strip()
                                        if 0 <= num < len(texts_to_translate) and translation:
                                            all_translations[num] = translation
                                            batch_translation_count += 1
                                    except:
                                        pass
                            print(f"[DEBUG] 第 {batch_start//batch_size + 1} 批成功翻译 {batch_translation_count} 个文本")
                        else:
                            print(f"[DEBUG] 第 {batch_start//batch_size + 1} 批翻译失败: {result.get('error')}")
                            failed_batches.append((batch_start, batch_end, batch_texts))
                    
                    # 对失败的批次进行逐个翻译
                    if failed_batches:
                        print(f"[DEBUG] 有 {len(failed_batches)} 个批次失败，尝试逐个翻译")
                        for batch_start, batch_end, batch_texts in failed_batches:
                            for i, text in enumerate(batch_texts):
                                global_idx = batch_start + i
                                if global_idx not in all_translations:  # 只翻译还没有翻译的
                                    single_prompt = prompt.replace("编号的", "").replace("请严格按照以下格式返回翻译结果，每行一个翻译，不要包含其他内容：\n[1] 翻译内容1\n[2] 翻译内容2\n...", "")
                                    result = call_ai_api(single_prompt, text, temperature=0.3)
                                    if result['success']:
                                        all_translations[global_idx] = result['content'].strip()
                                        print(f"[DEBUG] 成功单独翻译索引 {global_idx}")
                                    else:
                                        print(f"[DEBUG] 单独翻译索引 {global_idx} 失败")
                    
                    # 使用收集的所有翻译
                    translations = all_translations
                    print(f"[DEBUG] 批量翻译完成，共翻译 {len(translations)} 个文本")
                else:
                    # 文本数量不多，一次性处理
                    translations = {}
                    result = call_ai_api(prompt, combined_text, temperature=0.3)
                    
                    if result['success']:
                        # 解析翻译结果
                        print(f"\n[DEBUG] AI调用成功!")
                        print(f"[DEBUG] AI返回的原始内容:\n{result['content']}")
                        print(f"[DEBUG] 返回内容长度: {len(result['content'])}")
                        
                        translations = {}
                        lines = result['content'].strip().split('\n')
                        
                        print(f"[DEBUG] 分割后的行数: {len(lines)}")
                        
                        for line in lines:
                            line = line.strip()
                            if line:
                                # 尝试多种格式
                                # 格式1: [1] translation
                                if line.startswith('[') and ']' in line:
                                    try:
                                        bracket_end = line.index(']')
                                        num_str = line[1:bracket_end]
                                        num = int(num_str) - 1  # 转换为0索引
                                        translation = line[bracket_end + 1:].strip()
                                    
                                        if 0 <= num < len(texts_to_translate) and translation:
                                            translations[num] = translation
                                            print(f"[DEBUG] 解析成功 - 索引{num}: {translation}")
                                    except (ValueError, IndexError) as e:
                                        print(f"[DEBUG] 解析行失败: {line}, 错误: {e}")
                                # 格式2: 1. translation 或 1) translation
                                elif line[:3].strip() and (line[0].isdigit() or (len(line) > 1 and line[1].isdigit())):
                                    try:
                                        # 找到第一个分隔符
                                        for sep in ['. ', ') ', '、', '：', ': ']:
                                            if sep in line:
                                                num_str = line.split(sep)[0].strip()
                                                translation = line.split(sep, 1)[1].strip()
                                                if num_str.isdigit():
                                                    num = int(num_str) - 1
                                                    if 0 <= num < len(texts_to_translate) and translation:
                                                        translations[num] = translation
                                                        print(f"[DEBUG] 解析成功(格式2) - 索引{num}: {translation}")
                                                        break
                                    except Exception as e:
                                        print(f"[DEBUG] 解析行失败(格式2): {line}, 错误: {e}")
                    
                        # 将翻译结果映射回原始segments
                        print(f"\n[DEBUG] 开始映射翻译结果到segments")
                        print(f"[DEBUG] translations字典: {translations}")
                        print(f"[DEBUG] valid_indices: {valid_indices}")
                    
                        # 如果没有解析到任何翻译，尝试简单的逐行对应
                        if not translations and len(lines) >= len(texts_to_translate):
                            print("[DEBUG] 尝试简单的逐行对应")
                            for i in range(len(texts_to_translate)):
                                if i < len(lines) and lines[i].strip():
                                    translations[i] = lines[i].strip()
                                    print(f"[DEBUG] 逐行对应 - 索引{i}: {translations[i]}")
                    else:
                        # AI调用失败，使用原文作为翻译
                        print(f"[DEBUG] AI调用失败: {result.get('error', '未知错误')}")
                        print("[DEBUG] 所有segments使用原文")
                        translations = {}
                        for i, idx in enumerate(valid_indices):
                            translated_segments[idx] = {
                                **segments[idx],
                                'translated': segments[idx]['text']
                            }

                # 映射翻译结果到segments
                print(f"\n[DEBUG] 开始映射翻译结果到segments")
                print(f"[DEBUG] translations字典包含 {len(translations)} 个翻译")
                print(f"[DEBUG] valid_indices前10个: {valid_indices[:10]}...")

                # 映射翻译结果
                for i, idx in enumerate(valid_indices):
                    if i in translations:
                        translated_segments[idx] = {
                            **segments[idx],
                            "translated": translations[i]
                        }
                        print(f"[DEBUG] 映射成功 - segment[{idx}]的翻译: {translations[i][:50]}...")
                    else:
                        # 如果某个翻译失败，使用原文
                        translated_segments[idx] = {
                            **segments[idx],
                            "translated": segments[idx]['text']
                        }
                        print(f"[DEBUG] segment[{idx}]没有找到翻译（索引{i}），使用原文")
        else:
            # 没有需要翻译的文本
            print("[DEBUG] 没有需要翻译的文本，所有segments的text为空")
            for i, segment in enumerate(segments):
                translated_segments[i] = {
                    **segment,
                    'translated': segment.get('text', '')
                }
        
        # 生成双语字幕
        print(f"\n[DEBUG] 准备生成双语字幕")
        print(f"[DEBUG] translated_segments数量: {len(translated_segments)}")
        if translated_segments:
            print(f"[DEBUG] 第一个segment: {translated_segments[0]}")
            # 检查是否有translated字段
            has_translated = any('translated' in seg for seg in translated_segments)
            print(f"[DEBUG] 是否有任何segment包含translated字段: {has_translated}")
            if has_translated:
                for i, seg in enumerate(translated_segments[:3]):
                    if 'translated' in seg:
                        print(f"[DEBUG] Segment {i} 有翻译: {seg.get('translated', '')[:50]}...")
        
        dual_srt = generate_dual_language_srt(translated_segments)
        
        print(f"[DEBUG] 生成的双语字幕前500字符:\n{dual_srt[:500]}")
        
        # 保存文件
        filename = f"dual_subtitle_{datetime.now().strftime('%Y%m%d_%H%M%S')}.srt"
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(dual_srt)
        
        return jsonify({
            'success': True,
            'filename': filename,
            'content': dual_srt
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download/<filename>')
def download_file(filename):
    try:
        return send_file(
            os.path.join(app.config['OUTPUT_FOLDER'], filename),
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        return jsonify({'error': str(e)}), 404

def generate_srt(transcription_result):
    srt_content = []
    for i, segment in enumerate(transcription_result['segments'], 1):
        start_time = format_timestamp(segment['start'])
        end_time = format_timestamp(segment['end'])
        text = segment['text'].strip()
        
        srt_content.append(f"{i}")
        srt_content.append(f"{start_time} --> {end_time}")
        srt_content.append(text)
        srt_content.append("")
    
    return "\n".join(srt_content)

def generate_dual_language_srt(segments):
    srt_content = []
    print(f"\n[DEBUG generate_dual_language_srt] 处理{len(segments)}个segments")
    
    for i, segment in enumerate(segments, 1):
        start_time = format_timestamp(segment.get('start', 0))
        end_time = format_timestamp(segment.get('end', 0))
        original_text = segment.get('text', '').strip()
        translated_text = segment.get('translated', '').strip()
        
        print(f"[DEBUG] Segment {i}:")
        print(f"  - 原文: {original_text}")
        print(f"  - 译文: {translated_text}")
        print(f"  - 有translated字段: {'translated' in segment}")
        
        srt_content.append(f"{i}")
        srt_content.append(f"{start_time} --> {end_time}")
        srt_content.append(original_text)
        if translated_text and translated_text != original_text:
            srt_content.append(translated_text)
        srt_content.append("")
    
    return "\n".join(srt_content)

def format_timestamp(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}".replace('.', ',')

if __name__ == '__main__':
    print("\n" + "="*50)
    print("音频转文字应用 (集成AI工具)")
    print("="*50)
    print(f"AI模型：{AI_CONFIG['model']}")
    print(f"Whisper模型：支持 small 和 large-v3")
    print(f"访问地址：http://localhost:5680")
    print("="*50 + "\n")

    app.run(debug=True, host='0.0.0.0', port=5680)