# text2video环境使用说明

## 🎉 环境配置完成

您的音频转文字AI工具已经在conda环境`text2video`中成功配置完成！

## 📋 环境信息

- **环境名称**: text2video
- **Python版本**: 3.10
- **主要依赖**:
  - Flask (Web框架)
  - OpenAI Whisper (音频转文字)
  - PyTorch (深度学习框架，支持CUDA)
  - OpenAI API (AI文本处理)
  - FFmpeg (音频处理)

## 🚀 启动方式

### 方式一：使用启动脚本（推荐）
```bash
./start_text2video.sh
```

### 方式二：手动启动
```bash
# 激活环境
conda activate text2video

# 启动应用
python run_ai.py
```

## 🌐 访问应用

启动成功后，在浏览器中访问：
- http://localhost:5000
- http://127.0.0.1:5000

## ✨ 功能特点

### 🎯 核心功能
- **音频转文字**: 支持 wav、mp3、mp4、m4a、wma、aac 等多种格式
- **输出格式**: 纯文本（.txt）或字幕文件（.srt）
- **本地部署**: 模型完全在本地运行，保护隐私
- **GPU加速**: 自动检测并使用NVIDIA GPU加速

### 🤖 AI增强功能
- **智能总结**: 生成文本的简洁摘要
- **大纲提取**: 提取内容的主要观点和结构
- **关键词提取**: 识别文本中的重要关键词
- **文本润色**: 改进文本的表达和流畅度
- **多语言翻译**: 支持中英日韩等多语言翻译
- **自定义处理**: 支持用户自定义AI提示语

### 🌐 双语字幕
- 自动生成中英文双语字幕
- 支持多种目标语言（英、日、韩、西、法）
- SRT格式输出，兼容主流播放器

## 🔧 环境管理

### 查看环境列表
```bash
conda env list
```

### 激活环境
```bash
conda activate text2video
```

### 退出环境
```bash
conda deactivate
```

### 删除环境（如需要）
```bash
conda remove -n text2video --all
```

## 🛠️ 故障排除

### 1. 环境激活失败
```bash
# 重新初始化conda
source /home/<USER>/database/anaconda3/etc/profile.d/conda.sh
conda activate text2video
```

### 2. 依赖包缺失
```bash
conda activate text2video
pip install -r requirements.txt
```

### 3. GPU不可用
- 检查NVIDIA驱动是否正确安装
- 确认CUDA版本兼容性
- 应用会自动回退到CPU模式

### 4. 端口被占用
如果5000端口被占用，可以修改`app_with_ai.py`中的端口设置

## 📝 注意事项

1. **首次使用**: 首次运行时会自动下载Whisper模型，请确保网络连接正常
2. **AI功能**: 需要配置API密钥才能使用AI文本处理功能
3. **音频格式**: 建议使用清晰的音频文件以获得更好的识别效果
4. **GPU内存**: 使用大模型时需要足够的GPU内存

## 🎯 使用流程

1. 启动应用：`./start_text2video.sh`
2. 打开浏览器访问：http://localhost:5000
3. 上传音频文件
4. 选择输出格式（文本或字幕）
5. 开始转换
6. 使用AI工具进一步处理文本（可选）
7. 下载结果文件

## 📞 技术支持

如遇到问题，请检查：
1. conda环境是否正确激活
2. 所有依赖是否正确安装
3. 音频文件格式是否支持
4. 网络连接是否正常（AI功能需要）

---

**祝您使用愉快！** 🎉
