1
00:00:00,000 --> 00:00:04,100
欢迎收听豆包AI播客节目
Welcome to the Doubao AI Podcast.

2
00:00:06,920 --> 00:00:09,720
大家好 欢迎收听我们这期播客
Hello everyone, welcome to our podcast episode.

3
00:00:09,720 --> 00:00:11,260
然后咱们今天来聊一聊
Today, let's talk about

4
00:00:11,260 --> 00:00:15,360
蛋白质设计当中的人工智能和第一性原理的方法
artificial intelligence and first-principles methods in protein design.

5
00:00:15,360 --> 00:00:17,920
所以我们如何把这两种方法结合起来
So how can we combine these two approaches

6
00:00:17,920 --> 00:00:22,020
去解决一些多突变未典的这样的设计挑战
to tackle some multi-mutation design challenges?

7
00:00:22,020 --> 00:00:23,040
听起来很有意思
Sounds very interesting.

8
00:00:23,040 --> 00:00:24,060
那我们就直接开始吧
Let’s get started right away.

9
00:00:24,060 --> 00:00:25,600
那我们就先开始第一个问题
Let’s begin with the first question:

10
00:00:25,600 --> 00:00:30,200
就是说目前的这些蛋白质重新设计的方法
What are the major challenges or limitations

11
00:00:30,200 --> 00:00:32,760
都有哪些比较大的挑战或者说限制
in current protein redesign methods?

12
00:00:32,760 --> 00:00:37,120
然后我们这个研究是怎么样去聚焦与解决这些问题的
And how does our research focus on solving these issues?

13
00:00:37,120 --> 00:00:40,960
传统的方法它主要是基于第一性原理的能量含数
Traditional methods mainly rely on first-principles energy functions,

14
00:00:40,960 --> 00:00:43,780
那它会面临几个比较大的问题
but they face several major problems.

15
00:00:43,780 --> 00:00:48,120
一个是序列空间随着长度是指数级增长的
One is that the sequence space grows exponentially with length,

16
00:00:48,120 --> 00:00:50,440
所以这是一个非常大的搜索空间
creating a vast search space.

17
00:00:50,440 --> 00:00:54,520
第二个是你在搜索的时候很容易陷入局部罪优
The second is that the search easily gets trapped in local optima,

18
00:00:54,680 --> 00:00:57,400
然后导致你没有办法找到全局最优的解
making it hard to find the global optimal solution.

19
00:00:57,400 --> 00:01:01,640
那近年来深度学习的方法其实展示了非常大的潜力
In recent years, deep learning has shown great potential,

20
00:01:01,640 --> 00:01:05,800
比如说像tiffusion model可以去产生非常精确的backbone
such as diffusion models generating highly precise backbones.

21
00:01:05,800 --> 00:01:10,920
像protein mpn可以去针对一个backbone去设计出sequence
For example, protein MPN can design sequences targeting a specific backbone.

22
00:01:10,920 --> 00:01:14,800
但是这些方法在治疗性的设计这个领域里面
However, these methods are relatively scarce

23
00:01:14,800 --> 00:01:16,200
其实还是比较少的
in the field of therapeutic design.

24
00:01:16,200 --> 00:01:19,880
那主要用的还是一些已经比较成熟的分子类型
The primary tools used are still some well-established molecular types,

25
00:01:19,880 --> 00:01:20,520
比如抗体
such as antibodies.

26
00:01:20,520 --> 00:01:23,880
听起来就是说这个深度学习的方法虽然很有潜力
It sounds like although deep learning methods show great potential,

27
00:01:23,880 --> 00:01:28,160
但是好像在实际应用上面还是有一些瓶颈需要去突破的
there are still some bottlenecks to overcome in practical applications.

28
00:01:28,160 --> 00:01:29,240
对 是的
Yes, that's correct.

29
00:01:29,240 --> 00:01:33,640
尤其是在多突变位点的重新设计这个问题上面
Especially in the problem of redesigning multiple mutation sites,

30
00:01:33,640 --> 00:01:40,960
现在的工具它其实主要的验证还是在单点和双点的突变的数据级上面
current tools are mainly validated on single and double-point mutation datasets.

31
00:01:40,960 --> 00:01:48,960
那它很难去真正的反应治疗性设计这个场景下面的真实的需求
They struggle to truly reflect the real-world demands of therapeutic design scenarios,

32
00:01:48,960 --> 00:01:53,280
因为你可能需要去做一些同时很多个位点的突变
because you may need to perform mutations across many sites simultaneously,

33
00:01:53,280 --> 00:01:56,040
然后你要去预测它的结构和功能的变化
and then predict their structural and functional changes.

34
00:01:56,040 --> 00:01:57,760
这个机身是非常难的
This is extremely challenging.

35
00:01:57,760 --> 00:02:01,320
所以我们这个研究就是针对这样的一个挑战
Therefore, our research addresses this very challenge.

36
00:02:01,320 --> 00:02:05,120
我们选取了两类比较有代表性的方法
We selected two representative classes of methods

37
00:02:05,120 --> 00:02:08,600
去在多个场景下面去评估他们的能力
to evaluate their capabilities across multiple scenarios.

38
00:02:08,600 --> 00:02:14,080
那我们第一个挑战就是说要评估不同的蛋白质设计的工具和方法
Our first challenge was to assess different protein design tools and methods

39
00:02:14,080 --> 00:02:17,200
在面对多个位点同时突变的时候的表现
when facing simultaneous mutations at multiple sites.

40
00:02:17,200 --> 00:02:18,360
那我们是怎么做的呢
So how did we approach this?

41
00:02:18,360 --> 00:02:21,160
我们首先是建立了一个数据级
First, we established a dataset

42
00:02:21,160 --> 00:02:31,240
这个数据级是包含了36个Spectrum SH3结构域的酥水核心的多突变体
This dataset includes multiple mutants of the hydrophobic core across 36 Spectrum SH3 domains

43
00:02:31,240 --> 00:02:37,080
然后这些突变体我们都有非常精确的化学变性的稳定性的数据
For these mutants, we have highly precise data on their chemical denaturation stability

44
00:02:37,080 --> 00:02:41,800
其中有16个是我们用我们的新方法Track and Bind设计的
Among them, 16 were designed using our new method, Track and Bind

45
00:02:41,800 --> 00:02:45,600
我们解析了其中7个突变体的晶体结构
We resolved the crystal structures of 7 of these mutants

46
00:02:45,640 --> 00:02:51,040
所以我们现在对于这个SH3结构域的这个Hyroformic Core的突变的情况
So now we have a relatively in-depth understanding of mutations in the hydrophobic core of this SH3 domain

47
00:02:51,040 --> 00:02:52,560
是有一个比较深入的理解的
It sounds like you've put a lot of effort into this dataset

48
00:02:52,560 --> 00:02:55,600
听起来你们这个数据级真的是下了大功夫
Yes

49
00:02:55,600 --> 00:02:55,880
是啊
We also supplemented it with an additional 15 multi-mutant datasets selected from the literature

50
00:02:55,880 --> 00:03:00,440
然后我们还补充了从文献当中选取的另外15个
These are derived from different proteins

51
00:03:00,440 --> 00:03:04,000
来自于不同蛋白质的多突变体的数据
We tested three categories of methods in total

52
00:03:04,000 --> 00:03:06,480
我们总共测试了三类方法
The first category involves sequence-based structure prediction

53
00:03:06,480 --> 00:03:10,040
第一类方法是基于序列来预测结构的
For example, ESM Fold, L Fold 2, and RoseTTA Fold

54
00:03:10,040 --> 00:03:14,080
比如说像ESM Fold L Fold 2 Rosed Fold
The second category involves backbone-based sequence prediction

55
00:03:14,080 --> 00:03:17,440
第二类方法是从Backbomb来预测序列的
For example, ESM Inverse, ProteinMPNN, and our Track and Bind

56
00:03:17,440 --> 00:03:21,560
比如说像ESM Inverse Protein NPNN和我们的Track and Bind
The third category is force field-based methods

57
00:03:21,560 --> 00:03:24,200
第三类是基于Force Field的方法
For example, Rosetta and FoldX

58
00:03:24,200 --> 00:03:26,480
比如说像Rosetta和Fold X
Through this multi-dimensional testing, we can comprehensively evaluate

59
00:03:26,480 --> 00:03:31,160
我们通过这样一个多维度的测试就可以比较全面的去看到
The strengths and weaknesses of each method

60
00:03:31,160 --> 00:03:35,440
每一种方法它的优点和缺点到底在哪里

61
00:03:35,440 --> 00:03:38,880
为我们在实际应用当中去选择合适的方法
To help us choose appropriate methods in practical applications

62
00:03:38,880 --> 00:03:43,400
我们第二个挑战是要评估这个Inverse Folding的工具
Our second challenge is to evaluate this Inverse Folding tool

63
00:03:43,400 --> 00:03:50,120
在预测有很多突变的稳定的便体上面的表现
On its performance in predicting stable variants with many mutations

64
00:03:50,120 --> 00:03:51,160
那我们是怎么做的呢
So how did we do it?

65
00:03:51,160 --> 00:03:55,000
我们这次是使用了一个非常大的数据级
This time, we used a very large dataset

66
00:03:55,000 --> 00:03:58,720
这个数据级是包含了超过16万个
This dataset contains over 160,000

67
00:03:58,720 --> 00:04:02,480
single和double mutants的free energy的变化
Free energy changes of single and double mutants

68
00:04:02,480 --> 00:04:09,200
然后它是来自于155个artificial domains和58个natural domains
Derived from 155 artificial domains and 58 natural domains

69
00:04:09,200 --> 00:04:12,240
我们把这个实验测到的fitness的数据
We compared the experimentally measured fitness data

70
00:04:12,400 --> 00:04:15,520
和这些工具预测的序列进行一个比较
With the sequences predicted by these tools

71
00:04:15,520 --> 00:04:22,640
这些工具包括ESM Inverse, Protein, IPNN,Fold X,Rosetta和Track and Bind
These tools include ESM Inverse, Protein, IPNN, FoldX, Rosetta, and Track and Bind

72
00:04:22,640 --> 00:04:25,840
对,我们的Track and Bind在这个里面不光是用来设计mutant
Yes, our Track and Bind is not only used to design mutants here

73
00:04:25,840 --> 00:04:28,040
我们还把它当作一个scoring function来使用
We also employed it as a scoring function

74
00:04:28,040 --> 00:04:29,920
听起来是一个非常全面的评估
Sounds like a very comprehensive evaluation

75
00:04:29,920 --> 00:04:34,440
对,是的,我们这个里面涵盖了几乎所有的比较主流的工具
Yes, indeed, we covered almost all mainstream tools in this comparison

76
00:04:34,440 --> 00:04:37,520
然后通过这样一个评估我们就可以看到
Through this evaluation, we can observe

77
00:04:37,520 --> 00:04:40,440
比如说像Track and Bind和Protein, IPNN这样的方法
That methods like Track and Bind and Protein IPNN

78
00:04:40,520 --> 00:04:44,480
它在预测这些稳定的变体上面是表现非常好的
Perform very well in predicting these stable variants

79
00:04:44,480 --> 00:04:47,280
同时我们也发现就是没有一种方法是完美的
At the same time, we found that no single method is perfect

80
00:04:47,280 --> 00:04:50,120
所以可能未来还是需要把多种方法结合起来
So in the future, combining multiple methods may still be necessary

81
00:04:50,120 --> 00:04:52,880
才能够得到一个更好的结果
to achieve a better result

82
00:04:52,880 --> 00:04:58,760
我们使用Track and Bind来设计sh3结构域的多突变体的变体
We used Track and Bind to design multiple mutants of the SH3 domain

83
00:04:58,760 --> 00:05:00,600
然后我们是怎么做的呢?结果怎么样呢?
So how did we do it? And what were the results?

84
00:05:00,600 --> 00:05:06,560
我们选择了sh3结构域的hydrophobic core里面的9个residues
We selected 9 residues from the hydrophobic core of the SH3 domain

85
00:05:06,560 --> 00:05:08,200
然后我们使用了两个模板
Then we used two templates

86
00:05:08,200 --> 00:05:13,680
一个是它的野生型的结构ESHG和一个它的variant116G
One was its wild-type structure ESHG and the other was its variant 116G

87
00:05:13,680 --> 00:05:17,960
我们分别设计了8个mutants,总共是16个mutants
We designed 8 mutants for each, totaling 16 mutants

88
00:05:17,960 --> 00:05:20,880
我们在设计的过程当中我们是
During the design process, we

89
00:05:20,880 --> 00:05:24,560
首先把这9个核心的residues都突变成了Alany
First mutated all 9 core residues to alanine

90
00:05:24,560 --> 00:05:30,000
我们在运行Track and Bind的时候我们是排除了charged residues
When running Track and Bind, we excluded charged residues

91
00:05:30,000 --> 00:05:32,080
和一些large polar residues
and some large polar residues

92
00:05:32,080 --> 00:05:37,720
我们只选择那些至少有两个internal sidechain context的这样的tricks
We only selected tricks with at least two internal sidechain contexts

93
00:05:37,760 --> 00:05:42,520
我们最后得到了top500的这样的combinations
We finally obtained the top 500 combinations

94
00:05:42,520 --> 00:05:44,760
我们又经过了foldx的refinement
We then refined them using FoldX

95
00:05:44,760 --> 00:05:48,760
最后选择了16个candidates来进行实验验证
Finally, we selected 16 candidates for experimental validation

96
00:05:48,760 --> 00:05:51,640
听起来是一个非常系统的设计和筛选的过程
It sounds like a very systematic design and screening process

97
00:05:51,640 --> 00:05:55,440
对,是的,然后我们实验表达纯化了这16个mutants
Yes, exactly. Then we expressed and purified these 16 mutants

98
00:05:55,440 --> 00:05:58,200
并且测了他们的folding stability
and measured their folding stability

99
00:05:58,200 --> 00:06:02,840
我们发现有5个mutants它的稳定性是等于或者高于野生型的
We found that 5 mutants had stability equal to or higher than the wild-type

100
00:06:02,840 --> 00:06:07,240
有6个mutants是虽然稳定性下降了,但是它还是folded的
6 mutants showed reduced stability but remained folded

101
00:06:07,240 --> 00:06:09,760
有两个mutants是partially unfolded
Two mutants are partially unfolded

102
00:06:09,760 --> 00:06:11,880
还有三个mutants是mainly unfolded
Three other mutants are mainly unfolded

103
00:06:11,880 --> 00:06:15,600
我们同时也用foldx去model了这些设计
We also used FoldX to model these designs

104
00:06:15,600 --> 00:06:18,920
我们发现如果我们用野生型的结构作为模板的话
We found that if we used the wild-type structure as a template

105
00:06:18,920 --> 00:06:22,680
它的预测和实验的数据是比较相符的
The predictions matched the experimental data relatively well

106
00:06:22,680 --> 00:06:26,320
但是如果我们用expanded16G作为模板的话
However, if we used expanded16G as the template

107
00:06:26,320 --> 00:06:28,760
它的预测就会差得比较远
The predictions were significantly less accurate

108
00:06:28,760 --> 00:06:32,480
这个也是跟foldx它本身没有办法很好的处理
This is also related to FoldX's inherent limitation in handling

109
00:06:32,480 --> 00:06:34,400
有张力的结构是有关系的
Strained structures effectively

110
00:06:34,400 --> 00:06:39,840
我们在对这些有很多突变的sh3的变体进行研究的时候
When studying these SH3 variants with numerous mutations

111
00:06:39,840 --> 00:06:43,520
我们是怎么去分析实验测到的稳定性
How did we analyze the relationship between experimentally measured stability

112
00:06:43,520 --> 00:06:46,120
和这些工具预测的结果之间的关系的呢
And the results predicted by these tools?

113
00:06:46,120 --> 00:06:52,160
我们首先是把我们自己测得的16个sh3的mutants的稳定性的数据
First, we combined the stability data of 16 SH3 mutants we measured

114
00:06:52,160 --> 00:06:56,480
和文献当中的另外20个数据点合并到了一起
With an additional 20 data points from the literature

115
00:06:56,480 --> 00:07:00,760
所以我们总共有36个实验的deltaG的数据
Giving us a total of 36 experimental deltaG data points

116
00:07:00,760 --> 00:07:06,080
然后我们用20种不同的metrics对这些mutants进行了预测
Then we predicted these mutants using 20 different metrics

117
00:07:06,080 --> 00:07:09,080
这些metrics我们分成了四大类
These metrics were divided into four major categories

118
00:07:09,080 --> 00:07:12,640
第一类是基于fixbackbondscoring function
The first category is based on the fix-backbone scoring function

119
00:07:12,640 --> 00:07:16,880
比如说像chicombine,esm inverse,proteinnpnn等等
Such as chICombine, ESM-Inverse, ProteinMPNN, etc.

120
00:07:16,880 --> 00:07:19,160
第二类是基于ai的方法
The second category is AI-based methods

121
00:07:19,160 --> 00:07:23,480
直接从序列到结构预测再给你一个可靠性的分数
Directly predicting structure from sequence and providing a reliability score

122
00:07:23,480 --> 00:07:27,120
比如说像esmfold,rucetit,fold,lfold2
For example, ESMFold, RUCETit, Fold, LFold2

123
00:07:27,120 --> 00:07:33,040
第三类是把ai预测的结构再用foldx或者rucetit这样的firstfield来进行评估
The third category involves evaluating AI-predicted structures using tools like FoldX or RUCETit for first-field assessment

124
00:07:33,040 --> 00:07:37,240
第四类是在第三类的基础上面再加上一个relaxation的步骤
The fourth category adds a relaxation step on top of the third category

125
00:07:37,240 --> 00:07:40,000
听起来你们这个评估真的是非常全面
It sounds like your evaluation is really comprehensive

126
00:07:40,000 --> 00:07:42,920
对,是的,然后我们为了去比较这些方法
Yes, exactly. And to compare these methods,

127
00:07:42,920 --> 00:07:49,480
我们计算了true positive rate,true negative rate,balanced accuracy
We calculated the true positive rate, true negative rate, and balanced accuracy

128
00:07:49,480 --> 00:07:55,920
我们也计算了实验的deltaG和预测的scores之间的correlation
We also computed the correlation between experimental deltaG and predicted scores

129
00:07:55,920 --> 00:08:00,680
我们把所有的这些结果都化成了一个比较直观的图
We visualized all these results in an intuitive graph

130
00:08:00,680 --> 00:08:02,600
从这个图上面我们可以看到
From this graph, we can see that

131
00:08:02,600 --> 00:08:06,800
比如说像esmfoldf和rfanfold2这样的方法
Methods like ESMFold and RFANFold2

132
00:08:06,800 --> 00:08:10,960
它的balanced accuracy是可以达到08以上的
Can achieve a balanced accuracy above 0.8

133
00:08:10,960 --> 00:08:12,520
那这个就是一个比较好的方法
Which indicates a relatively good method

134
00:08:12,520 --> 00:08:13,920
那我们今天聊了这么多
We’ve covered a lot today

135
00:08:13,920 --> 00:08:19,720
从传统的蛋白质设计的挑战到深度学习方法的潜力
From the challenges of traditional protein design to the potential of deep learning methods

136
00:08:19,720 --> 00:08:24,720
然后再到我们tracombine以及其他的工具的全面的评估
And then to our comprehensive evaluation of Tracombine and other tools

137
00:08:24,720 --> 00:08:29,360
可以看到就是说现在这个领域还是在快速的发展的
It’s clear that this field is still rapidly evolving

138
00:08:29,360 --> 00:08:33,480
未来可能还是会需要把多种方法结合起来
In the future, combining multiple methods may still be necessary

139
00:08:33,480 --> 00:08:36,200
才能够去解决这些比较复杂的问题
To solve these complex problems

140
00:08:36,200 --> 00:08:36,840
对,OK了
Yes, OK.
