1
00:00:00,000 --> 00:00:04,100
欢迎收听豆包AI播客节目
[Translation to en]: 欢迎收听豆包AI播客节目

2
00:00:06,920 --> 00:00:09,720
大家好 欢迎收听我们这期播客
[Translation to en]: 大家好 欢迎收听我们这期播客

3
00:00:09,720 --> 00:00:11,260
然后咱们今天来聊一聊
[Translation to en]: 然后咱们今天来聊一聊

4
00:00:11,260 --> 00:00:15,360
蛋白质设计当中的人工智能和第一性原理的方法
[Translation to en]: 蛋白质设计当中的人工智能和第一性原理的方法

5
00:00:15,360 --> 00:00:17,920
所以我们如何把这两种方法结合起来
[Translation to en]: 所以我们如何把这两种方法结合起来

6
00:00:17,920 --> 00:00:21,760
去解决一些多突变未典的设计挑战
[Translation to en]: 去解决一些多突变未典的设计挑战

7
00:00:21,760 --> 00:00:23,040
听起来很有意思
[Translation to en]: 听起来很有意思

8
00:00:23,040 --> 00:00:24,060
那我们就直接开始吧
[Translation to en]: 那我们就直接开始吧

9
00:00:24,060 --> 00:00:25,600
那我们就先开始第一个问题
[Translation to en]: 那我们就先开始第一个问题

10
00:00:25,600 --> 00:00:30,200
就是说目前的这些蛋白质重新设计的方法
[Translation to en]: 就是说目前的这些蛋白质重新设计的方法

11
00:00:30,200 --> 00:00:32,760
都有哪些比较大的挑战或者说限制
[Translation to en]: 都有哪些比较大的挑战或者说限制

12
00:00:32,760 --> 00:00:37,120
然后我们这个研究是怎么样去聚焦与解决这些问题的
[Translation to en]: 然后我们这个研究是怎么样去聚焦与解决这些问题的

13
00:00:37,120 --> 00:00:40,960
传统的方法它主要是基于第一性原理的能量含数
[Translation to en]: 传统的方法它主要是基于第一性原理的能量含数

14
00:00:40,960 --> 00:00:43,780
它会面临几个比较大的问题
[Translation to en]: 它会面临几个比较大的问题

15
00:00:43,780 --> 00:00:48,120
一个是序列空间随着长度是指数级增长的
[Translation to en]: 一个是序列空间随着长度是指数级增长的

16
00:00:48,120 --> 00:00:50,440
所以这是一个非常大的搜索空间
[Translation to en]: 所以这是一个非常大的搜索空间

17
00:00:50,440 --> 00:00:54,520
第二个是你在搜索的时候很容易陷入局部罪优
[Translation to en]: 第二个是你在搜索的时候很容易陷入局部罪优

18
00:00:54,640 --> 00:00:57,400
然后导致你没有办法找到全局最优的解
[Translation to en]: 然后导致你没有办法找到全局最优的解

19
00:00:57,400 --> 00:01:01,640
那近年来深度学习的方法其实展示了非常大的潜力
[Translation to en]: 那近年来深度学习的方法其实展示了非常大的潜力

20
00:01:01,640 --> 00:01:05,740
比如说像tiffusion model可以去产生非常精确的backbone
[Translation to en]: 比如说像tiffusion model可以去产生非常精确的backbone

21
00:01:05,740 --> 00:01:10,880
像pro-tink npnn可以去针对一个backbone去设计出sequence
[Translation to en]: 像pro-tink npnn可以去针对一个backbone去设计出sequence

22
00:01:10,880 --> 00:01:14,800
但是这些方法在治疗性的设计这个领域里面
[Translation to en]: 但是这些方法在治疗性的设计这个领域里面

23
00:01:14,800 --> 00:01:16,200
其实还是比较少的
[Translation to en]: 其实还是比较少的

24
00:01:16,200 --> 00:01:19,840
那主要用的还是一些已经比较成熟的分子类型
[Translation to en]: 那主要用的还是一些已经比较成熟的分子类型

25
00:01:19,840 --> 00:01:20,480
比如抗体
[Translation to en]: 比如抗体

26
00:01:20,480 --> 00:01:23,840
听起来就是说这个深度学习的方法虽然很有潜力
[Translation to en]: 听起来就是说这个深度学习的方法虽然很有潜力

27
00:01:23,840 --> 00:01:28,160
但是好像在实际应用上面还是有一些瓶颈需要去突破的
[Translation to en]: 但是好像在实际应用上面还是有一些瓶颈需要去突破的

28
00:01:28,160 --> 00:01:29,280
对 是的
[Translation to en]: 对 是的

29
00:01:29,280 --> 00:01:33,640
尤其是在多突变位点的重新设计这个问题上面
[Translation to en]: 尤其是在多突变位点的重新设计这个问题上面

30
00:01:33,640 --> 00:01:40,960
现在的工具它其实主要的验证还是在单点和双点的突变的数据级上面
[Translation to en]: 现在的工具它其实主要的验证还是在单点和双点的突变的数据级上面

31
00:01:40,960 --> 00:01:48,960
那它很难去真正的反应治疗性设计这个场景下面的真实的需求
[Translation to en]: 那它很难去真正的反应治疗性设计这个场景下面的真实的需求

32
00:01:48,960 --> 00:01:53,280
因为你可能需要去做一些同时很多个位点的突变
[Translation to en]: 因为你可能需要去做一些同时很多个位点的突变

33
00:01:53,280 --> 00:01:56,040
然后你要去预测它的结构和功能的变化
[Translation to en]: 然后你要去预测它的结构和功能的变化

34
00:01:56,040 --> 00:01:57,760
这个机身是非常难的
[Translation to en]: 这个机身是非常难的

35
00:01:57,760 --> 00:02:01,320
所以我们这个研究就是针对这样的一个挑战
[Translation to en]: 所以我们这个研究就是针对这样的一个挑战

36
00:02:01,320 --> 00:02:05,120
我们选取了两类比较有代表性的方法
[Translation to en]: 我们选取了两类比较有代表性的方法

37
00:02:05,120 --> 00:02:08,600
去在多个场景下面去评估他们的能力
[Translation to en]: 去在多个场景下面去评估他们的能力

38
00:02:08,600 --> 00:02:14,080
那我们第一个挑战就是说要评估不同的蛋白质设计的工具和方法
[Translation to en]: 那我们第一个挑战就是说要评估不同的蛋白质设计的工具和方法

39
00:02:14,080 --> 00:02:17,200
在面对多个位点同时突变的时候的表现
[Translation to en]: 在面对多个位点同时突变的时候的表现

40
00:02:17,200 --> 00:02:18,360
那我们是怎么做的呢
[Translation to en]: 那我们是怎么做的呢

41
00:02:18,360 --> 00:02:21,160
我们首先是建立了一个数据级
[Translation to en]: 我们首先是建立了一个数据级

42
00:02:21,160 --> 00:02:31,240
这个数据级是包含了36个Spectrum SH3结构域的酥水核心的多突变体
[Translation to en]: 这个数据级是包含了36个Spectrum SH3结构域的酥水核心的多突变体

43
00:02:31,240 --> 00:02:37,080
然后这些突变体我们都有非常精确的化学变性的稳定性的数据
[Translation to en]: 然后这些突变体我们都有非常精确的化学变性的稳定性的数据

44
00:02:37,080 --> 00:02:41,800
其中有16个是我们用我们的新方法Track and Bind设计的
[Translation to en]: 其中有16个是我们用我们的新方法Track and Bind设计的

45
00:02:41,800 --> 00:02:45,600
我们解析了其中7个突变体的晶体结构
[Translation to en]: 我们解析了其中7个突变体的晶体结构

46
00:02:45,600 --> 00:02:51,040
所以我们现在对于这个SH3结构域的Hydrofomacore的突变的情况
[Translation to en]: 所以我们现在对于这个SH3结构域的Hydrofomacore的突变的情况

47
00:02:51,040 --> 00:02:52,600
是有一个比较深入的理解的
[Translation to en]: 是有一个比较深入的理解的

48
00:02:52,600 --> 00:02:55,640
听起来你们这个数据级真的是下了大功夫
[Translation to en]: 听起来你们这个数据级真的是下了大功夫

49
00:02:55,640 --> 00:02:55,920
是啊
[Translation to en]: 是啊

50
00:02:55,920 --> 00:03:00,480
然后我们还补充了从文献当中选取的另外15个
[Translation to en]: 然后我们还补充了从文献当中选取的另外15个

51
00:03:00,480 --> 00:03:04,040
来自于不同蛋白质的多突变体的数据
[Translation to en]: 来自于不同蛋白质的多突变体的数据

52
00:03:04,040 --> 00:03:06,520
我们总共测试了三类方法
[Translation to en]: 我们总共测试了三类方法

53
00:03:06,520 --> 00:03:10,040
第一类方法是基于序列来预测结构的
[Translation to en]: 第一类方法是基于序列来预测结构的

54
00:03:10,040 --> 00:03:14,080
比如说像ESMFoldAlford2RosterVold
[Translation to en]: 比如说像ESMFoldAlford2RosterVold

55
00:03:14,080 --> 00:03:17,520
第二类方法是从Backbomb来预测序列的
[Translation to en]: 第二类方法是从Backbomb来预测序列的

56
00:03:17,560 --> 00:03:21,560
比如说像ESMInverseProteinNPNn和我们的Track and Bind
[Translation to en]: 比如说像ESMInverseProteinNPNn和我们的Track and Bind

57
00:03:21,560 --> 00:03:24,240
第三类是基于ForceField的方法
[Translation to en]: 第三类是基于ForceField的方法

58
00:03:24,240 --> 00:03:26,520
比如说像Rosetta和FoldAx
[Translation to en]: 比如说像Rosetta和FoldAx

59
00:03:26,520 --> 00:03:28,720
我们通过这样一个多维度的测试
[Translation to en]: 我们通过这样一个多维度的测试

60
00:03:28,720 --> 00:03:32,720
就可以比较全面的去看到每一种方法
[Translation to en]: 就可以比较全面的去看到每一种方法

61
00:03:32,720 --> 00:03:35,480
它的优点和缺点到底在哪里
[Translation to en]: 它的优点和缺点到底在哪里

62
00:03:35,480 --> 00:03:38,920
为我们在实际应用当中去选择合适的方法
[Translation to en]: 为我们在实际应用当中去选择合适的方法

63
00:03:38,920 --> 00:03:43,440
我们第二个挑战是要评估这个InverseFolding的工具
[Translation to en]: 我们第二个挑战是要评估这个InverseFolding的工具

64
00:03:43,440 --> 00:03:50,120
在预测有很多突变的稳定的便体上面的表现
[Translation to en]: 在预测有很多突变的稳定的便体上面的表现

65
00:03:50,120 --> 00:03:51,160
那我们是怎么做的呢
[Translation to en]: 那我们是怎么做的呢

66
00:03:51,160 --> 00:03:55,000
我们这次是使用了一个非常大的数据级
[Translation to en]: 我们这次是使用了一个非常大的数据级

67
00:03:55,000 --> 00:03:58,720
这个数据级是包含了超过16万个
[Translation to en]: 这个数据级是包含了超过16万个

68
00:03:58,720 --> 00:04:02,480
single和double mutants的free energy的变化
[Translation to en]: single和double mutants的free energy的变化

69
00:04:02,480 --> 00:04:06,840
然后它是来自于155个artificial domains
[Translation to en]: 然后它是来自于155个artificial domains

70
00:04:06,840 --> 00:04:09,200
和58个natural domains
[Translation to en]: 和58个natural domains

71
00:04:09,200 --> 00:04:12,240
我们把这个实验测到的fitness的数据
[Translation to en]: 我们把这个实验测到的fitness的数据

72
00:04:12,400 --> 00:04:15,520
和这些工具预测的序列进行一个比较
[Translation to en]: 和这些工具预测的序列进行一个比较

73
00:04:15,520 --> 00:04:18,280
这些工具包括ESM inverse
[Translation to en]: 这些工具包括ESM inverse

74
00:04:18,280 --> 00:04:22,640
protein,npn,FoldAx,Rosetta和Track combined
[Translation to en]: protein,npn,FoldAx,Rosetta和Track combined

75
00:04:22,640 --> 00:04:25,840
对我们的Track combined在这个里面不光是用来设计multant
[Translation to en]: 对我们的Track combined在这个里面不光是用来设计multant

76
00:04:25,840 --> 00:04:28,040
我们还把它当作一个scoring function来使用
[Translation to en]: 我们还把它当作一个scoring function来使用

77
00:04:28,040 --> 00:04:29,920
听起来是一个非常全面的评估
[Translation to en]: 听起来是一个非常全面的评估

78
00:04:29,920 --> 00:04:34,440
对是的我们这个里面涵盖了几乎所有的比较主流的工具
[Translation to en]: 对是的我们这个里面涵盖了几乎所有的比较主流的工具

79
00:04:34,440 --> 00:04:37,520
然后通过这样一个评估我们就可以看到
[Translation to en]: 然后通过这样一个评估我们就可以看到

80
00:04:37,520 --> 00:04:40,440
比如说像Track combined和protein,npn这样的方法
[Translation to en]: 比如说像Track combined和protein,npn这样的方法

81
00:04:40,520 --> 00:04:44,480
它在预测这些稳定的便体上面是表现非常好的
[Translation to en]: 它在预测这些稳定的便体上面是表现非常好的

82
00:04:44,480 --> 00:04:47,280
同时我们也发现就是没有异种方法是完美的
[Translation to en]: 同时我们也发现就是没有异种方法是完美的

83
00:04:47,280 --> 00:04:50,120
所以可能未来还是需要把多种方法结合起来
[Translation to en]: 所以可能未来还是需要把多种方法结合起来

84
00:04:50,120 --> 00:04:52,880
才能够得到一个更好的结果
[Translation to en]: 才能够得到一个更好的结果

85
00:04:52,880 --> 00:04:58,760
我们使用Track combined来设计sh3结构域的多突变体的便体
[Translation to en]: 我们使用Track combined来设计sh3结构域的多突变体的便体

86
00:04:58,760 --> 00:05:00,600
然后我们是怎么做的呢结果怎么样呢
[Translation to en]: 然后我们是怎么做的呢结果怎么样呢

87
00:05:00,600 --> 00:05:06,560
我们选择了这个sh3结构域的hydrophobic core里面的9个residues
[Translation to en]: 我们选择了这个sh3结构域的hydrophobic core里面的9个residues

88
00:05:06,560 --> 00:05:08,200
然后我们使用了两个模板
[Translation to en]: 然后我们使用了两个模板

89
00:05:08,240 --> 00:05:13,720
一个是它的野生型的结构ESHG和一个它的variant116G
[Translation to en]: 一个是它的野生型的结构ESHG和一个它的variant116G

90
00:05:13,720 --> 00:05:18,000
我们分别设计了8个mutants,总共是16个mutants
[Translation to en]: 我们分别设计了8个mutants,总共是16个mutants

91
00:05:18,000 --> 00:05:20,920
我们在设计的过程当中我们是
[Translation to en]: 我们在设计的过程当中我们是

92
00:05:20,920 --> 00:05:24,600
首先把这9个核心的residues都突变成了alynine
[Translation to en]: 首先把这9个核心的residues都突变成了alynine

93
00:05:24,600 --> 00:05:30,040
我们在运行Track combined的时候我们是排除了charged residues
[Translation to en]: 我们在运行Track combined的时候我们是排除了charged residues

94
00:05:30,040 --> 00:05:32,120
和一些large polar residues
[Translation to en]: 和一些large polar residues

95
00:05:32,120 --> 00:05:37,760
我们只选择那些至少有两个internal sidechain context的这样的tricks
[Translation to en]: 我们只选择那些至少有两个internal sidechain context的这样的tricks

96
00:05:37,800 --> 00:05:42,560
我们最后得到了top500的这样的combinations
[Translation to en]: 我们最后得到了top500的这样的combinations

97
00:05:42,560 --> 00:05:44,760
我们又经过了foldx的refinement
[Translation to en]: 我们又经过了foldx的refinement

98
00:05:44,760 --> 00:05:48,760
最后选择了16个candidates来进行实验验证
[Translation to en]: 最后选择了16个candidates来进行实验验证

99
00:05:48,760 --> 00:05:51,680
听起来是一个非常系统的设计和筛选的过程
[Translation to en]: 听起来是一个非常系统的设计和筛选的过程

100
00:05:51,680 --> 00:05:55,440
对,是的,然后我们实验表达纯化了这16个mutants
[Translation to en]: 对,是的,然后我们实验表达纯化了这16个mutants

101
00:05:55,440 --> 00:05:58,240
并且测了他们的folding stability
[Translation to en]: 并且测了他们的folding stability

102
00:05:58,240 --> 00:06:02,880
我们发现有5个mutants它的稳定性是等于或者高于野生型的
[Translation to en]: 我们发现有5个mutants它的稳定性是等于或者高于野生型的

103
00:06:02,880 --> 00:06:07,280
有6个mutants是虽然稳定性下降了,但是它还是folded的
[Translation to en]: 有6个mutants是虽然稳定性下降了,但是它还是folded的

104
00:06:07,360 --> 00:06:09,840
有两个mutants是partially unfolded
[Translation to en]: 有两个mutants是partially unfolded

105
00:06:09,840 --> 00:06:12,000
还有三个mutants是mainly unfolded
[Translation to en]: 还有三个mutants是mainly unfolded

106
00:06:12,000 --> 00:06:15,600
我们同时也用foldx去model了这些设计
[Translation to en]: 我们同时也用foldx去model了这些设计

107
00:06:15,600 --> 00:06:19,040
我们发现如果我们用野生型的结构作为模板的话
[Translation to en]: 我们发现如果我们用野生型的结构作为模板的话

108
00:06:19,040 --> 00:06:22,800
它的预测和实验的数据是比较相符的
[Translation to en]: 它的预测和实验的数据是比较相符的

109
00:06:22,800 --> 00:06:26,440
但是如果我们用expanded16G作为模板的话
[Translation to en]: 但是如果我们用expanded16G作为模板的话

110
00:06:26,440 --> 00:06:28,840
它的预测就会差得比较远
[Translation to en]: 它的预测就会差得比较远

111
00:06:28,840 --> 00:06:32,600
这个也是跟foldx它本身没有办法很好的处理
[Translation to en]: 这个也是跟foldx它本身没有办法很好的处理

112
00:06:32,600 --> 00:06:34,480
有张力的结构是有关系的
[Translation to en]: 有张力的结构是有关系的

113
00:06:34,560 --> 00:06:39,840
我们在对这些有很多突变的sh3的辨体进行研究的时候
[Translation to en]: 我们在对这些有很多突变的sh3的辨体进行研究的时候

114
00:06:39,840 --> 00:06:43,520
我们是怎么去分析这个实验测到的稳定性
[Translation to en]: 我们是怎么去分析这个实验测到的稳定性

115
00:06:43,520 --> 00:06:46,160
和这些工具预测的结果之间的关系的呢
[Translation to en]: 和这些工具预测的结果之间的关系的呢

116
00:06:46,160 --> 00:06:52,200
我们首先是把我们自己测得的16个sh3的mutants的稳定性的数据
[Translation to en]: 我们首先是把我们自己测得的16个sh3的mutants的稳定性的数据

117
00:06:52,200 --> 00:06:56,480
和文献当中的另外20个数据点合并到一起
[Translation to en]: 和文献当中的另外20个数据点合并到一起

118
00:06:56,480 --> 00:07:00,760
所以我们总共有36个实验的deltaG的数据
[Translation to en]: 所以我们总共有36个实验的deltaG的数据

119
00:07:00,760 --> 00:07:06,080
然后我们用20种不同的metrics对这些mutants进行了预测
[Translation to en]: 然后我们用20种不同的metrics对这些mutants进行了预测

120
00:07:06,080 --> 00:07:09,080
这些metrics我们分成了四大类
[Translation to en]: 这些metrics我们分成了四大类

121
00:07:09,080 --> 00:07:12,640
第一类是基于fixbackbondscoring function
[Translation to en]: 第一类是基于fixbackbondscoring function

122
00:07:12,640 --> 00:07:16,880
比如说像chicombine,esm inverse,proteinnpnn等等
[Translation to en]: 比如说像chicombine,esm inverse,proteinnpnn等等

123
00:07:16,880 --> 00:07:19,160
第二类是基于ai的方法
[Translation to en]: 第二类是基于ai的方法

124
00:07:19,160 --> 00:07:23,480
直接从序列到结构预测再给你一个可靠性的分数
[Translation to en]: 直接从序列到结构预测再给你一个可靠性的分数

125
00:07:23,480 --> 00:07:27,120
比如说像esmfold,rucetit,fold,lfold2
[Translation to en]: 比如说像esmfold,rucetit,fold,lfold2

126
00:07:27,160 --> 00:07:30,320
第三类是把ai预测的结构再用foldx
[Translation to en]: 第三类是把ai预测的结构再用foldx

127
00:07:30,320 --> 00:07:33,040
或者rucetit这样的firstfield来进行评估
[Translation to en]: 或者rucetit这样的firstfield来进行评估

128
00:07:33,040 --> 00:07:37,240
第四类是在第三类的基础上面再加上一个relaxation的步骤
[Translation to en]: 第四类是在第三类的基础上面再加上一个relaxation的步骤

129
00:07:37,240 --> 00:07:40,000
听起来你们这个评估真的是非常全面
[Translation to en]: 听起来你们这个评估真的是非常全面

130
00:07:40,000 --> 00:07:42,920
对,是的,然后我们为了去比较这些方法
[Translation to en]: 对,是的,然后我们为了去比较这些方法

131
00:07:42,920 --> 00:07:49,480
我们计算了true positive rate,true negative rate,balanced accuracy
[Translation to en]: 我们计算了true positive rate,true negative rate,balanced accuracy

132
00:07:49,480 --> 00:07:55,920
我们也计算了实验的deltaG和预测的scores之间的correlation
[Translation to en]: 我们也计算了实验的deltaG和预测的scores之间的correlation

133
00:07:55,960 --> 00:08:00,720
我们把所有的这些结果都化成了一个比较直观的图
[Translation to en]: 我们把所有的这些结果都化成了一个比较直观的图

134
00:08:00,720 --> 00:08:02,640
从这个图上面我们可以看到
[Translation to en]: 从这个图上面我们可以看到

135
00:08:02,640 --> 00:08:06,840
比如说像esmfoldf和rfanfold2这样的方法
[Translation to en]: 比如说像esmfoldf和rfanfold2这样的方法

136
00:08:06,840 --> 00:08:11,000
它的balanced accuracy是可以达到08以上的
[Translation to en]: 它的balanced accuracy是可以达到08以上的

137
00:08:11,000 --> 00:08:12,560
那这个就是一个比较好的方法
[Translation to en]: 那这个就是一个比较好的方法

138
00:08:12,560 --> 00:08:13,920
那我们今天聊了这么多
[Translation to en]: 那我们今天聊了这么多

139
00:08:13,920 --> 00:08:19,760
从传统的蛋白质设计的挑战到深度学习方法的潜力
[Translation to en]: 从传统的蛋白质设计的挑战到深度学习方法的潜力

140
00:08:19,760 --> 00:08:24,720
然后再到我们tracombine以及其他的工具的全面的评估
[Translation to en]: 然后再到我们tracombine以及其他的工具的全面的评估

141
00:08:24,720 --> 00:08:29,360
可以看到就是说现在这个领域还是在快速的发展的
[Translation to en]: 可以看到就是说现在这个领域还是在快速的发展的

142
00:08:29,360 --> 00:08:33,480
未来可能还是会需要把多种方法结合起来
[Translation to en]: 未来可能还是会需要把多种方法结合起来

143
00:08:33,480 --> 00:08:36,160
才能够去解决这些比较复杂的问题
[Translation to en]: 才能够去解决这些比较复杂的问题

144
00:08:36,160 --> 00:08:36,840
对,OK了
[Translation to en]: 对,OK了
