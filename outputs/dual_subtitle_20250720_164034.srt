1
00:00:00,000 --> 00:00:04,100
欢迎收听豆包AI播客节目
欢迎收听豆包AI播客节目

2
00:00:06,920 --> 00:00:09,720
大家好 欢迎收听我们这期播客
大家好 欢迎收听我们这期播客

3
00:00:09,720 --> 00:00:11,260
然后咱们今天来聊一聊
然后咱们今天来聊一聊

4
00:00:11,260 --> 00:00:15,360
蛋白质设计当中的人工智能和第一性原理的方法
蛋白质设计当中的人工智能和第一性原理的方法

5
00:00:15,360 --> 00:00:17,920
所以我们如何把这两种方法结合起来
所以我们如何把这两种方法结合起来

6
00:00:17,920 --> 00:00:22,020
去解决一些多突变未典的这样的设计挑战
去解决一些多突变未典的这样的设计挑战

7
00:00:22,020 --> 00:00:23,040
听起来很有意思
听起来很有意思

8
00:00:23,040 --> 00:00:24,060
那我们就直接开始吧
那我们就直接开始吧

9
00:00:24,060 --> 00:00:25,600
那我们就先开始第一个问题
那我们就先开始第一个问题

10
00:00:25,600 --> 00:00:30,200
就是说目前的这些蛋白质重新设计的方法
就是说目前的这些蛋白质重新设计的方法

11
00:00:30,200 --> 00:00:32,760
都有哪些比较大的挑战或者说限制
都有哪些比较大的挑战或者说限制

12
00:00:32,760 --> 00:00:37,120
然后我们这个研究是怎么样去聚焦与解决这些问题的
然后我们这个研究是怎么样去聚焦与解决这些问题的

13
00:00:37,120 --> 00:00:40,960
传统的方法它主要是基于第一性原理的能量含数
传统的方法它主要是基于第一性原理的能量含数

14
00:00:40,960 --> 00:00:43,780
那它会面临几个比较大的问题
那它会面临几个比较大的问题

15
00:00:43,780 --> 00:00:48,120
一个是序列空间随着长度是指数级增长的
一个是序列空间随着长度是指数级增长的

16
00:00:48,120 --> 00:00:50,440
所以这是一个非常大的搜索空间
所以这是一个非常大的搜索空间

17
00:00:50,440 --> 00:00:54,520
第二个是你在搜索的时候很容易陷入局部罪优
第二个是你在搜索的时候很容易陷入局部罪优

18
00:00:54,680 --> 00:00:57,400
然后导致你没有办法找到全局最优的解
然后导致你没有办法找到全局最优的解

19
00:00:57,400 --> 00:01:01,640
那近年来深度学习的方法其实展示了非常大的潜力
那近年来深度学习的方法其实展示了非常大的潜力

20
00:01:01,640 --> 00:01:05,800
比如说像tiffusion model可以去产生非常精确的backbone
比如说像tiffusion model可以去产生非常精确的backbone

21
00:01:05,800 --> 00:01:10,920
像protein mpn可以去针对一个backbone去设计出sequence
像protein mpn可以去针对一个backbone去设计出sequence

22
00:01:10,920 --> 00:01:14,800
但是这些方法在治疗性的设计这个领域里面
但是这些方法在治疗性的设计这个领域里面

23
00:01:14,800 --> 00:01:16,200
其实还是比较少的
其实还是比较少的

24
00:01:16,200 --> 00:01:19,880
那主要用的还是一些已经比较成熟的分子类型
那主要用的还是一些已经比较成熟的分子类型

25
00:01:19,880 --> 00:01:20,520
比如抗体
比如抗体

26
00:01:20,520 --> 00:01:23,880
听起来就是说这个深度学习的方法虽然很有潜力
听起来就是说这个深度学习的方法虽然很有潜力

27
00:01:23,880 --> 00:01:28,160
但是好像在实际应用上面还是有一些瓶颈需要去突破的
但是好像在实际应用上面还是有一些瓶颈需要去突破的

28
00:01:28,160 --> 00:01:29,240
对 是的
对 是的

29
00:01:29,240 --> 00:01:33,640
尤其是在多突变位点的重新设计这个问题上面
尤其是在多突变位点的重新设计这个问题上面

30
00:01:33,640 --> 00:01:40,960
现在的工具它其实主要的验证还是在单点和双点的突变的数据级上面
现在的工具它其实主要的验证还是在单点和双点的突变的数据级上面

31
00:01:40,960 --> 00:01:48,960
那它很难去真正的反应治疗性设计这个场景下面的真实的需求
那它很难去真正的反应治疗性设计这个场景下面的真实的需求

32
00:01:48,960 --> 00:01:53,280
因为你可能需要去做一些同时很多个位点的突变
因为你可能需要去做一些同时很多个位点的突变

33
00:01:53,280 --> 00:01:56,040
然后你要去预测它的结构和功能的变化
然后你要去预测它的结构和功能的变化

34
00:01:56,040 --> 00:01:57,760
这个机身是非常难的
这个机身是非常难的

35
00:01:57,760 --> 00:02:01,320
所以我们这个研究就是针对这样的一个挑战
所以我们这个研究就是针对这样的一个挑战

36
00:02:01,320 --> 00:02:05,120
我们选取了两类比较有代表性的方法
我们选取了两类比较有代表性的方法

37
00:02:05,120 --> 00:02:08,600
去在多个场景下面去评估他们的能力
去在多个场景下面去评估他们的能力

38
00:02:08,600 --> 00:02:14,080
那我们第一个挑战就是说要评估不同的蛋白质设计的工具和方法
那我们第一个挑战就是说要评估不同的蛋白质设计的工具和方法

39
00:02:14,080 --> 00:02:17,200
在面对多个位点同时突变的时候的表现
在面对多个位点同时突变的时候的表现

40
00:02:17,200 --> 00:02:18,360
那我们是怎么做的呢
那我们是怎么做的呢

41
00:02:18,360 --> 00:02:21,160
我们首先是建立了一个数据级
我们首先是建立了一个数据级

42
00:02:21,160 --> 00:02:31,240
这个数据级是包含了36个Spectrum SH3结构域的酥水核心的多突变体
这个数据级是包含了36个Spectrum SH3结构域的酥水核心的多突变体

43
00:02:31,240 --> 00:02:37,080
然后这些突变体我们都有非常精确的化学变性的稳定性的数据
然后这些突变体我们都有非常精确的化学变性的稳定性的数据

44
00:02:37,080 --> 00:02:41,800
其中有16个是我们用我们的新方法Track and Bind设计的
其中有16个是我们用我们的新方法Track and Bind设计的

45
00:02:41,800 --> 00:02:45,600
我们解析了其中7个突变体的晶体结构
我们解析了其中7个突变体的晶体结构

46
00:02:45,640 --> 00:02:51,040
所以我们现在对于这个SH3结构域的这个Hyroformic Core的突变的情况
所以我们现在对于这个SH3结构域的这个Hyroformic Core的突变的情况

47
00:02:51,040 --> 00:02:52,560
是有一个比较深入的理解的
是有一个比较深入的理解的

48
00:02:52,560 --> 00:02:55,600
听起来你们这个数据级真的是下了大功夫
听起来你们这个数据级真的是下了大功夫

49
00:02:55,600 --> 00:02:55,880
是啊
是啊

50
00:02:55,880 --> 00:03:00,440
然后我们还补充了从文献当中选取的另外15个
然后我们还补充了从文献当中选取的另外15个

51
00:03:00,440 --> 00:03:04,000
来自于不同蛋白质的多突变体的数据
来自于不同蛋白质的多突变体的数据

52
00:03:04,000 --> 00:03:06,480
我们总共测试了三类方法
我们总共测试了三类方法

53
00:03:06,480 --> 00:03:10,040
第一类方法是基于序列来预测结构的
第一类方法是基于序列来预测结构的

54
00:03:10,040 --> 00:03:14,080
比如说像ESM Fold L Fold 2 Rosed Fold
比如说像ESM Fold L Fold 2 Rosed Fold

55
00:03:14,080 --> 00:03:17,440
第二类方法是从Backbomb来预测序列的
第二类方法是从Backbomb来预测序列的

56
00:03:17,440 --> 00:03:21,560
比如说像ESM Inverse Protein NPNN和我们的Track and Bind
比如说像ESM Inverse Protein NPNN和我们的Track and Bind

57
00:03:21,560 --> 00:03:24,200
第三类是基于Force Field的方法
第三类是基于Force Field的方法

58
00:03:24,200 --> 00:03:26,480
比如说像Rosetta和Fold X
比如说像Rosetta和Fold X

59
00:03:26,480 --> 00:03:31,160
我们通过这样一个多维度的测试就可以比较全面的去看到
我们通过这样一个多维度的测试就可以比较全面的去看到

60
00:03:31,160 --> 00:03:35,440
每一种方法它的优点和缺点到底在哪里
每一种方法它的优点和缺点到底在哪里

61
00:03:35,440 --> 00:03:38,880
为我们在实际应用当中去选择合适的方法
为我们在实际应用当中去选择合适的方法

62
00:03:38,880 --> 00:03:43,400
我们第二个挑战是要评估这个Inverse Folding的工具
我们第二个挑战是要评估这个Inverse Folding的工具

63
00:03:43,400 --> 00:03:50,120
在预测有很多突变的稳定的便体上面的表现
在预测有很多突变的稳定的便体上面的表现

64
00:03:50,120 --> 00:03:51,160
那我们是怎么做的呢
那我们是怎么做的呢

65
00:03:51,160 --> 00:03:55,000
我们这次是使用了一个非常大的数据级
我们这次是使用了一个非常大的数据级

66
00:03:55,000 --> 00:03:58,720
这个数据级是包含了超过16万个
这个数据级是包含了超过16万个

67
00:03:58,720 --> 00:04:02,480
single和double mutants的free energy的变化
single和double mutants的free energy的变化

68
00:04:02,480 --> 00:04:09,200
然后它是来自于155个artificial domains和58个natural domains
然后它是来自于155个artificial domains和58个natural domains

69
00:04:09,200 --> 00:04:12,240
我们把这个实验测到的fitness的数据
我们把这个实验测到的fitness的数据

70
00:04:12,400 --> 00:04:15,520
和这些工具预测的序列进行一个比较
和这些工具预测的序列进行一个比较

71
00:04:15,520 --> 00:04:22,640
这些工具包括ESM Inverse, Protein, IPNN,Fold X,Rosetta和Track and Bind
这些工具包括ESM Inverse, Protein, IPNN,Fold X,Rosetta和Track and Bind

72
00:04:22,640 --> 00:04:25,840
对,我们的Track and Bind在这个里面不光是用来设计mutant
对,我们的Track and Bind在这个里面不光是用来设计mutant

73
00:04:25,840 --> 00:04:28,040
我们还把它当作一个scoring function来使用
我们还把它当作一个scoring function来使用

74
00:04:28,040 --> 00:04:29,920
听起来是一个非常全面的评估
听起来是一个非常全面的评估

75
00:04:29,920 --> 00:04:34,440
对,是的,我们这个里面涵盖了几乎所有的比较主流的工具
对,是的,我们这个里面涵盖了几乎所有的比较主流的工具

76
00:04:34,440 --> 00:04:37,520
然后通过这样一个评估我们就可以看到
然后通过这样一个评估我们就可以看到

77
00:04:37,520 --> 00:04:40,440
比如说像Track and Bind和Protein, IPNN这样的方法
比如说像Track and Bind和Protein, IPNN这样的方法

78
00:04:40,520 --> 00:04:44,480
它在预测这些稳定的变体上面是表现非常好的
它在预测这些稳定的变体上面是表现非常好的

79
00:04:44,480 --> 00:04:47,280
同时我们也发现就是没有一种方法是完美的
同时我们也发现就是没有一种方法是完美的

80
00:04:47,280 --> 00:04:50,120
所以可能未来还是需要把多种方法结合起来
所以可能未来还是需要把多种方法结合起来

81
00:04:50,120 --> 00:04:52,880
才能够得到一个更好的结果
才能够得到一个更好的结果

82
00:04:52,880 --> 00:04:58,760
我们使用Track and Bind来设计sh3结构域的多突变体的变体
我们使用Track and Bind来设计sh3结构域的多突变体的变体

83
00:04:58,760 --> 00:05:00,600
然后我们是怎么做的呢?结果怎么样呢?
然后我们是怎么做的呢?结果怎么样呢?

84
00:05:00,600 --> 00:05:06,560
我们选择了sh3结构域的hydrophobic core里面的9个residues
我们选择了sh3结构域的hydrophobic core里面的9个residues

85
00:05:06,560 --> 00:05:08,200
然后我们使用了两个模板
然后我们使用了两个模板

86
00:05:08,200 --> 00:05:13,680
一个是它的野生型的结构ESHG和一个它的variant116G
一个是它的野生型的结构ESHG和一个它的variant116G

87
00:05:13,680 --> 00:05:17,960
我们分别设计了8个mutants,总共是16个mutants
我们分别设计了8个mutants,总共是16个mutants

88
00:05:17,960 --> 00:05:20,880
我们在设计的过程当中我们是
我们在设计的过程当中我们是

89
00:05:20,880 --> 00:05:24,560
首先把这9个核心的residues都突变成了Alany
首先把这9个核心的residues都突变成了Alany

90
00:05:24,560 --> 00:05:30,000
我们在运行Track and Bind的时候我们是排除了charged residues
我们在运行Track and Bind的时候我们是排除了charged residues

91
00:05:30,000 --> 00:05:32,080
和一些large polar residues
和一些large polar residues

92
00:05:32,080 --> 00:05:37,720
我们只选择那些至少有两个internal sidechain context的这样的tricks
我们只选择那些至少有两个internal sidechain context的这样的tricks

93
00:05:37,760 --> 00:05:42,520
我们最后得到了top500的这样的combinations
我们最后得到了top500的这样的combinations

94
00:05:42,520 --> 00:05:44,760
我们又经过了foldx的refinement
我们又经过了foldx的refinement

95
00:05:44,760 --> 00:05:48,760
最后选择了16个candidates来进行实验验证
最后选择了16个candidates来进行实验验证

96
00:05:48,760 --> 00:05:51,640
听起来是一个非常系统的设计和筛选的过程
听起来是一个非常系统的设计和筛选的过程

97
00:05:51,640 --> 00:05:55,440
对,是的,然后我们实验表达纯化了这16个mutants
对,是的,然后我们实验表达纯化了这16个mutants

98
00:05:55,440 --> 00:05:58,200
并且测了他们的folding stability
并且测了他们的folding stability

99
00:05:58,200 --> 00:06:02,840
我们发现有5个mutants它的稳定性是等于或者高于野生型的
我们发现有5个mutants它的稳定性是等于或者高于野生型的

100
00:06:02,840 --> 00:06:07,240
有6个mutants是虽然稳定性下降了,但是它还是folded的
有6个mutants是虽然稳定性下降了,但是它还是folded的

101
00:06:07,240 --> 00:06:09,760
有两个mutants是partially unfolded
有两个mutants是partially unfolded

102
00:06:09,760 --> 00:06:11,880
还有三个mutants是mainly unfolded
还有三个mutants是mainly unfolded

103
00:06:11,880 --> 00:06:15,600
我们同时也用foldx去model了这些设计
我们同时也用foldx去model了这些设计

104
00:06:15,600 --> 00:06:18,920
我们发现如果我们用野生型的结构作为模板的话
我们发现如果我们用野生型的结构作为模板的话

105
00:06:18,920 --> 00:06:22,680
它的预测和实验的数据是比较相符的
它的预测和实验的数据是比较相符的

106
00:06:22,680 --> 00:06:26,320
但是如果我们用expanded16G作为模板的话
但是如果我们用expanded16G作为模板的话

107
00:06:26,320 --> 00:06:28,760
它的预测就会差得比较远
它的预测就会差得比较远

108
00:06:28,760 --> 00:06:32,480
这个也是跟foldx它本身没有办法很好的处理
这个也是跟foldx它本身没有办法很好的处理

109
00:06:32,480 --> 00:06:34,400
有张力的结构是有关系的
有张力的结构是有关系的

110
00:06:34,400 --> 00:06:39,840
我们在对这些有很多突变的sh3的变体进行研究的时候
我们在对这些有很多突变的sh3的变体进行研究的时候

111
00:06:39,840 --> 00:06:43,520
我们是怎么去分析实验测到的稳定性
我们是怎么去分析实验测到的稳定性

112
00:06:43,520 --> 00:06:46,120
和这些工具预测的结果之间的关系的呢
和这些工具预测的结果之间的关系的呢

113
00:06:46,120 --> 00:06:52,160
我们首先是把我们自己测得的16个sh3的mutants的稳定性的数据
我们首先是把我们自己测得的16个sh3的mutants的稳定性的数据

114
00:06:52,160 --> 00:06:56,480
和文献当中的另外20个数据点合并到了一起
和文献当中的另外20个数据点合并到了一起

115
00:06:56,480 --> 00:07:00,760
所以我们总共有36个实验的deltaG的数据
所以我们总共有36个实验的deltaG的数据

116
00:07:00,760 --> 00:07:06,080
然后我们用20种不同的metrics对这些mutants进行了预测
然后我们用20种不同的metrics对这些mutants进行了预测

117
00:07:06,080 --> 00:07:09,080
这些metrics我们分成了四大类
这些metrics我们分成了四大类

118
00:07:09,080 --> 00:07:12,640
第一类是基于fixbackbondscoring function
第一类是基于fixbackbondscoring function

119
00:07:12,640 --> 00:07:16,880
比如说像chicombine,esm inverse,proteinnpnn等等
比如说像chicombine,esm inverse,proteinnpnn等等

120
00:07:16,880 --> 00:07:19,160
第二类是基于ai的方法
第二类是基于ai的方法

121
00:07:19,160 --> 00:07:23,480
直接从序列到结构预测再给你一个可靠性的分数
直接从序列到结构预测再给你一个可靠性的分数

122
00:07:23,480 --> 00:07:27,120
比如说像esmfold,rucetit,fold,lfold2
比如说像esmfold,rucetit,fold,lfold2

123
00:07:27,120 --> 00:07:33,040
第三类是把ai预测的结构再用foldx或者rucetit这样的firstfield来进行评估
第三类是把ai预测的结构再用foldx或者rucetit这样的firstfield来进行评估

124
00:07:33,040 --> 00:07:37,240
第四类是在第三类的基础上面再加上一个relaxation的步骤
第四类是在第三类的基础上面再加上一个relaxation的步骤

125
00:07:37,240 --> 00:07:40,000
听起来你们这个评估真的是非常全面
听起来你们这个评估真的是非常全面

126
00:07:40,000 --> 00:07:42,920
对,是的,然后我们为了去比较这些方法
对,是的,然后我们为了去比较这些方法

127
00:07:42,920 --> 00:07:49,480
我们计算了true positive rate,true negative rate,balanced accuracy
我们计算了true positive rate,true negative rate,balanced accuracy

128
00:07:49,480 --> 00:07:55,920
我们也计算了实验的deltaG和预测的scores之间的correlation
我们也计算了实验的deltaG和预测的scores之间的correlation

129
00:07:55,920 --> 00:08:00,680
我们把所有的这些结果都化成了一个比较直观的图
我们把所有的这些结果都化成了一个比较直观的图

130
00:08:00,680 --> 00:08:02,600
从这个图上面我们可以看到
从这个图上面我们可以看到

131
00:08:02,600 --> 00:08:06,800
比如说像esmfoldf和rfanfold2这样的方法
比如说像esmfoldf和rfanfold2这样的方法

132
00:08:06,800 --> 00:08:10,960
它的balanced accuracy是可以达到08以上的
它的balanced accuracy是可以达到08以上的

133
00:08:10,960 --> 00:08:12,520
那这个就是一个比较好的方法
那这个就是一个比较好的方法

134
00:08:12,520 --> 00:08:13,920
那我们今天聊了这么多
那我们今天聊了这么多

135
00:08:13,920 --> 00:08:19,720
从传统的蛋白质设计的挑战到深度学习方法的潜力
从传统的蛋白质设计的挑战到深度学习方法的潜力

136
00:08:19,720 --> 00:08:24,720
然后再到我们tracombine以及其他的工具的全面的评估
然后再到我们tracombine以及其他的工具的全面的评估

137
00:08:24,720 --> 00:08:29,360
可以看到就是说现在这个领域还是在快速的发展的
可以看到就是说现在这个领域还是在快速的发展的

138
00:08:29,360 --> 00:08:33,480
未来可能还是会需要把多种方法结合起来
未来可能还是会需要把多种方法结合起来

139
00:08:33,480 --> 00:08:36,200
才能够去解决这些比较复杂的问题
才能够去解决这些比较复杂的问题

140
00:08:36,200 --> 00:08:36,840
对,OK了
对,OK了
